#helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
#helm repo update
#helm install --namespace monitoring prometheus-operator-crds prometheus-community/prometheus-operator-crds -f prometheus-operator-crds-values.yaml --create-namespace

# This chart only installs CRDs, so minimal configuration is needed
# Enable all CRDs
alertmanagers:
  enabled: true
podmonitors:
  enabled: true
probes:
  enabled: true
prometheuses:
  enabled: true
prometheusrules:
  enabled: true
servicemonitors:
  enabled: true
thanosrulers:
  enabled: true

# Don't clean up CRDs on chart uninstall
cleanup:
  # If true, the release will clean up the CRDs before installing new ones
  crds: false
