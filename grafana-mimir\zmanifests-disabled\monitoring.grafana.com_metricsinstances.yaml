---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
  creationTimestamp: null
  name: metricsinstances.monitoring.grafana.com
spec:
  group: monitoring.grafana.com
  names:
    categories:
    - agent-operator
    kind: MetricsInstance
    listKind: MetricsInstanceList
    plural: metricsinstances
    singular: metricsinstance
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              additionalScrapeConfigs:
                properties:
                  key:
                    type: string
                  name:
                    type: string
                  optional:
                    type: boolean
                required:
                - key
                type: object
                x-kubernetes-map-type: atomic
              maxWALTime:
                type: string
              minWALTime:
                type: string
              podMonitorNamespaceSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              podMonitorSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              probeNamespaceSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              probeSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              remoteFlushDeadline:
                type: string
              remoteWrite:
                items:
                  properties:
                    basicAuth:
                      properties:
                        password:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        username:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    bearerToken:
                      type: string
                    bearerTokenFile:
                      type: string
                    headers:
                      additionalProperties:
                        type: string
                      type: object
                    metadataConfig:
                      properties:
                        send:
                          type: boolean
                        sendInterval:
                          type: string
                      type: object
                    name:
                      type: string
                    oauth2:
                      properties:
                        clientId:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        clientSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        endpointParams:
                          additionalProperties:
                            type: string
                          type: object
                        scopes:
                          items:
                            type: string
                          type: array
                        tokenUrl:
                          minLength: 1
                          type: string
                      required:
                      - clientId
                      - clientSecret
                      - tokenUrl
                      type: object
                    proxyUrl:
                      type: string
                    queueConfig:
                      properties:
                        batchSendDeadline:
                          type: string
                        capacity:
                          type: integer
                        maxBackoff:
                          type: string
                        maxRetries:
                          type: integer
                        maxSamplesPerSend:
                          type: integer
                        maxShards:
                          type: integer
                        minBackoff:
                          type: string
                        minShards:
                          type: integer
                        retryOnRateLimit:
                          type: boolean
                      type: object
                    remoteTimeout:
                      type: string
                    sigv4:
                      properties:
                        accessKey:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        profile:
                          type: string
                        region:
                          type: string
                        roleARN:
                          type: string
                        secretKey:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    tlsConfig:
                      properties:
                        ca:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        caFile:
                          type: string
                        cert:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        certFile:
                          type: string
                        insecureSkipVerify:
                          type: boolean
                        keyFile:
                          type: string
                        keySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        serverName:
                          type: string
                      type: object
                    url:
                      type: string
                    writeRelabelConfigs:
                      items:
                        properties:
                          action:
                            default: replace
                            enum:
                            - replace
                            - Replace
                            - keep
                            - Keep
                            - drop
                            - Drop
                            - hashmod
                            - HashMod
                            - labelmap
                            - LabelMap
                            - labeldrop
                            - LabelDrop
                            - labelkeep
                            - LabelKeep
                            - lowercase
                            - Lowercase
                            - uppercase
                            - Uppercase
                            - keepequal
                            - KeepEqual
                            - dropequal
                            - DropEqual
                            type: string
                          modulus:
                            format: int64
                            type: integer
                          regex:
                            type: string
                          replacement:
                            type: string
                          separator:
                            type: string
                          sourceLabels:
                            items:
                              pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                              type: string
                            type: array
                          targetLabel:
                            type: string
                        type: object
                      type: array
                  required:
                  - url
                  type: object
                type: array
              serviceMonitorNamespaceSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              serviceMonitorSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              walTruncateFrequency:
                type: string
              writeStaleOnShutdown:
                type: boolean
            type: object
        type: object
    served: true
    storage: true
