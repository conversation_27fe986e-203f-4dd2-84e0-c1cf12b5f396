# ---
# apiVersion: apiextensions.k8s.io/v1
# kind: CustomResourceDefinition
# metadata:
#   annotations:
#     controller-gen.kubebuilder.io/version: v0.9.2
#   creationTimestamp: null
#   name: podlogs.monitoring.grafana.com
# spec:
#   group: monitoring.grafana.com
#   names:
#     categories:
#     - agent-operator
#     kind: PodLogs
#     listKind: PodLogsList
#     plural: podlogs
#     singular: podlogs
#   scope: Namespaced
#   versions:
#   - name: v1alpha1
#     schema:
#       openAPIV3Schema:
#         properties:
#           apiVersion:
#             type: string
#           kind:
#             type: string
#           metadata:
#             type: object
#           spec:
#             properties:
#               jobLabel:
#                 type: string
#               namespaceSelector:
#                 properties:
#                   any:
#                     type: boolean
#                   matchNames:
#                     items:
#                       type: string
#                     type: array
#                 type: object
#               pipelineStages:
#                 items:
#                   properties:
#                     cri:
#                       type: object
#                     docker:
#                       type: object
#                     drop:
#                       properties:
#                         dropCounterReason:
#                           type: string
#                         expression:
#                           type: string
#                         longerThan:
#                           type: string
#                         olderThan:
#                           type: string
#                         source:
#                           type: string
#                         value:
#                           type: string
#                       type: object
#                     json:
#                       properties:
#                         expressions:
#                           additionalProperties:
#                             type: string
#                           type: object
#                         source:
#                           type: string
#                       type: object
#                     labelAllow:
#                       items:
#                         type: string
#                       type: array
#                     labelDrop:
#                       items:
#                         type: string
#                       type: array
#                     labels:
#                       additionalProperties:
#                         type: string
#                       type: object
#                     limit:
#                       properties:
#                         burst:
#                           type: integer
#                         drop:
#                           type: boolean
#                         rate:
#                           type: integer
#                       type: object
#                     match:
#                       properties:
#                         action:
#                           type: string
#                         dropCounterReason:
#                           type: string
#                         pipelineName:
#                           type: string
#                         selector:
#                           type: string
#                         stages:
#                           type: string
#                       required:
#                       - selector
#                       type: object
#                     metrics:
#                       additionalProperties:
#                         properties:
#                           action:
#                             type: string
#                           buckets:
#                             items:
#                               type: string
#                             type: array
#                           countEntryBytes:
#                             type: boolean
#                           description:
#                             type: string
#                           matchAll:
#                             type: boolean
#                           maxIdleDuration:
#                             type: string
#                           prefix:
#                             type: string
#                           source:
#                             type: string
#                           type:
#                             type: string
#                           value:
#                             type: string
#                         required:
#                         - action
#                         - type
#                         type: object
#                       type: object
#                     multiline:
#                       properties:
#                         firstLine:
#                           type: string
#                         maxLines:
#                           type: integer
#                         maxWaitTime:
#                           type: string
#                       required:
#                       - firstLine
#                       type: object
#                     output:
#                       properties:
#                         source:
#                           type: string
#                       required:
#                       - source
#                       type: object
#                     pack:
#                       properties:
#                         ingestTimestamp:
#                           type: boolean
#                         labels:
#                           items:
#                             type: string
#                           type: array
#                       required:
#                       - labels
#                       type: object
#                     regex:
#                       properties:
#                         expression:
#                           type: string
#                         source:
#                           type: string
#                       required:
#                       - expression
#                       type: object
#                     replace:
#                       properties:
#                         expression:
#                           type: string
#                         replace:
#                           type: string
#                         source:
#                           type: string
#                       required:
#                       - expression
#                       type: object
#                     template:
#                       properties:
#                         source:
#                           type: string
#                         template:
#                           type: string
#                       required:
#                       - source
#                       - template
#                       type: object
#                     tenant:
#                       properties:
#                         label:
#                           type: string
#                         source:
#                           type: string
#                         value:
#                           type: string
#                       type: object
#                     timestamp:
#                       properties:
#                         actionOnFailure:
#                           type: string
#                         fallbackFormats:
#                           items:
#                             type: string
#                           type: array
#                         format:
#                           type: string
#                         location:
#                           type: string
#                         source:
#                           type: string
#                       required:
#                       - format
#                       - source
#                       type: object
#                   type: object
#                 type: array
#               podTargetLabels:
#                 items:
#                   type: string
#                 type: array
#               relabelings:
#                 items:
#                   properties:
#                     action:
#                       default: replace
#                       enum:
#                       - replace
#                       - Replace
#                       - keep
#                       - Keep
#                       - drop
#                       - Drop
#                       - hashmod
#                       - HashMod
#                       - labelmap
#                       - LabelMap
#                       - labeldrop
#                       - LabelDrop
#                       - labelkeep
#                       - LabelKeep
#                       - lowercase
#                       - Lowercase
#                       - uppercase
#                       - Uppercase
#                       - keepequal
#                       - KeepEqual
#                       - dropequal
#                       - DropEqual
#                       type: string
#                     modulus:
#                       format: int64
#                       type: integer
#                     regex:
#                       type: string
#                     replacement:
#                       type: string
#                     separator:
#                       type: string
#                     sourceLabels:
#                       items:
#                         pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
#                         type: string
#                       type: array
#                     targetLabel:
#                       type: string
#                   type: object
#                 type: array
#               selector:
#                 properties:
#                   matchExpressions:
#                     items:
#                       properties:
#                         key:
#                           type: string
#                         operator:
#                           type: string
#                         values:
#                           items:
#                             type: string
#                           type: array
#                       required:
#                       - key
#                       - operator
#                       type: object
#                     type: array
#                   matchLabels:
#                     additionalProperties:
#                       type: string
#                     type: object
#                 type: object
#                 x-kubernetes-map-type: atomic
#             required:
#             - selector
#             type: object
#         type: object
#     served: true
#     storage: true
