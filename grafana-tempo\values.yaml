replicas: 1

tempo:
  memBallastSizeMbs: 1024
  multitenancyEnabled: false
  # -- If true, Tempo will report anonymous usage data about the shape of a deployment to Grafana Labs
  reportingEnabled: false
  metricsGenerator:
    # -- If true, enables Tempo's metrics generator (https://grafana.com/docs/tempo/next/metrics-generator/)
    enabled: true
    #remoteWriteUrl: "http://prometheus.monitoring:9090/api/v1/write"
    remoteWriteUrl: "http://grafana-mimir-nginx.infrastructure.svc:80/api/v1/push"

  storage:
    trace:
      # tempo storage backend.
      # Refers to: https://grafana.com/docs/tempo/latest/configuration/#storage
      ## Use s3 for example
      # backend: s3
      # store traces in s3
      # s3:
      #   bucket: <your s3 bucket>                        # store traces in this bucket
      #   endpoint: s3.dualstack.us-east-2.amazonaws.com  # api endpoint
      #   access_key: ...                                 # optional. access key when using static credentials.
      #   secret_key: ...                                 # optional. secret key when using static credentials.
      #   insecure: false                                 # optional. enable if endpoint is http
      backend: local
      local:
        path: /var/tempo/traces
      wal:
        path: /var/tempo/wal    

  receivers:
    jaeger:
      protocols:
        grpc:
          endpoint: 0.0.0.0:14250
        thrift_binary:
          endpoint: 0.0.0.0:6832
        thrift_compact:
          endpoint: 0.0.0.0:6831
        thrift_http:
          endpoint: 0.0.0.0:14268
    opencensus:
    otlp:
      protocols:
        grpc:
          endpoint: "0.0.0.0:4317"
        http:
          endpoint: "0.0.0.0:4318"

  ingress:
    enabled: false


persistence:
  enabled: false
  # -- Enable StatefulSetAutoDeletePVC feature
  enableStatefulSetAutoDeletePVC: false
  # storageClassName: local-path
  accessModes:
    - ReadWriteOnce
  size: 10Gi