cluster:
  name: "eks-observability-prod"

global:
  scrapeInterval: 30s #with 60s we have gaps in small graph intervals like 5-60 mins

clusterMetrics:
  enabled: true 
  # extraPorts:
  #   node_exporter:
  #     port: 9101  # move off the conflicting 9100
  controlPlane:  #subchart: https://github.com/grafana/k8s-monitoring-helm/blob/main/charts/k8s-monitoring/charts/feature-cluster-metrics/values.yaml
    # -- enable all Kubernetes Control Plane metrics sources. This includes api-server, kube-scheduler,
    # kube-controller-manager, and KubeDNS.
    # @section -- Control Plane
    enabled: false #set it to false as we enabled all separately with fill allow list
  apiServer:
    enabled: true
    jobLabel: "integrations/kubernetes/kube-apiserver"
    metricsTuning: #forcing to get all metrics w/o filtering
      useDefaultAllowList: false
      useIntegrationAllowList: false
      includeMetrics:
        - ".*"
      # no exclusions
      excludeMetrics: []        
  kubeScheduler:    
    enabled: true
  kubeControllerManager:
    enabled: true
  kubeDNS:
    enabled: true
    jobLabel: "integrations/kubernetes/kube-dns"
  kubelet:
    enabled: true
    jobLabel: "integrations/kubernetes/kubeletprobes"  
    metricsTuning:
      useDefaultAllowList: false
  kubeletResource:  
    enabled: true   
    jobLabel: "integrations/kubernetes/resources"
    metricsTuning:
      useDefaultAllowList: false
  kubeletProbes:
    enabled: true
    jobLabel: "integrations/kubernetes/probes"
    metricsTuning:
      useDefaultAllowList: false
  cadvisor:
    enabled: true
    jobLabel: "integrations/kubernetes/cadvisor"    
    metricsTuning:
      # -- Filter the list of metrics from cAdvisor to the minimal set required for Kubernetes Monitoring.
      # @section -- cAdvisor; default = true
      useDefaultAllowList: false
      # -- Only keep filesystem metrics that use the following physical devices
      # @section -- cAdvisor
      keepPhysicalFilesystemDevices: ["mmcblk.p.+", "nvme.+", "rbd.+", "sd.+", "vd.+", "xvd.+", "dasd.+"]
      # -- Only keep network metrics that use the following physical devices
      # @section -- cAdvisor
      keepPhysicalNetworkDevices: ["en[iospx][0-9].*", "eth[0-9].*"] # all for Bottlerocket
      includeNamespaces: []
      # -- For metrics with a `namespace` label, drop those that are in this list.
      # @section -- cAdvisor
      excludeNamespaces: []   
      #scrapeInterval: ""    #defauls 60s

  kubeProxy:
    enabled: true
    jobLabel: "integrations/kubernetes/kube-proxy"    
  kube-state-metrics:
    enabled: true
    jobLabel: "integrations/kubernetes/kube-state-metrics"    
    metricsTuning:
      useDefaultAllowList: false
  node-exporter:
    enabled: true
    jobLabel: "integrations/kubernetes/node_exporter"    
    metricsTuning:
      useDefaultAllowList: false
      useIntegrationAllowList: false
  
  #For the Cluster Metrics feature, Windows Exporter is disabled but it will still be deployed.
  #If you do not want these metrics, disable the deployment by setting: clusterMetrics: windows-exporter: enabled: false deploy: false 
  #If you do want these metrics, enable it by setting: clusterMetrics: windows-exporter: enabled: true Use --debug flag to render out invalid YAML
  windows-exporter:
    enabled: false
    deploy: false 
  keepler:
    enabled: false
  opencost:  
    carbonCost:
      enabled: false
    cloudCost: 
      enabled: false  




clusterEvents:
  enabled: true
  collector: alloy-logs

alloy-metrics:
  enabled: true
  # https://github.com/grafana/alloy/tree/main/operations/helm/charts/alloy
  # Any built-in metrics collectors remain as configured…  
  extraConfig: |
    // Scrape Karpenter metrics every 30s and forward into your Mimir remote-write
    prometheus.scrape "karpenter_metrics" {      
      job_name = "integrations/karpenter"
      targets = [
        { __address__ = "karpenter.karpenter.svc.cluster.local:8080" },
      ]
      scheme           = "http"
      scrape_interval = "30s"
      metrics_path    = "/metrics"
      //bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
      tls_config {
        insecure_skip_verify = true
      }
      forward_to = [ prometheus.remote_write.grafana_mimir.receiver ]
      //forward_to = argument.metrics_destinations.value
    }    

    // ─────────────────────────────────────────────────────────────
    // NO NEED to override the built-in relabel for "apiserver" with a catch-all.
    //    By giving it the exact same component name, the Operator will
    //    replace the old one.
    //prometheus.relabel "apiserver" {
    //  max_cache_size = 100000
    //
    //  // keep **all** metric names
    //  rule {
    //    source_labels = ["__name__"]
    //    regex         = ".*"
    //    action        = "keep"
    //  }
    //
    //  // forward to your existing remote-write pipeline
    //  forward_to = [prometheus.remote_write.grafana_mimir.receiver]
    //}    

  serviceMonitor: #it will monitor alloy itself
    enabled: true  
    # // ───────────────────────────────────────────────────────────────────────
    # //  Redefine the 'apiserver' scrape *in place* so we lose the chart's
    # //  tiny keep-list and get every metric.
    # prometheus.scrape "apiserver" {
    #   job_name         = "integrations/kubernetes/kube-apiserver"
    #   // inline the single target for your API-server
    #   targets          = [{ __address__ = "kubernetes.default.svc:443" }]

    #   scheme           = "https"
    #   metrics_path     = "/metrics"
    #   bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
    #   tls_config {
    #     insecure_skip_verify = true
    #   }

    #   // pull *every* metric, no name-filtering:
    #   scrape_interval  = "60s"
    #   forward_to       = [prometheus.remote_write.grafana_mimir.receiver]
    # }    


alloy-singleton:
  enabled: true
  # extraArgs:
  #   - --log.level=debug  

alloy-logs:
  enabled: true
  alloy:
    mounts:
      varlog: true  
    clustering:
      enabled: true      

alloy-operator:
  deploy: true

# alloy-profiles:
#   # -- Deploy the Alloy instance for gathering profiles.
#   # @section -- Collectors - Alloy Profiles
#   enabled: false

  
podLogs:
  enabled: true
  #gatherMethod: kubernetesApi # if use API - disable alloy-logs mounts
  collector: alloy-logs
  labelsToKeep: ["app_kubernetes_io_name","container","instance","job","level","namespace","service_name","service_namespace","deployment_environment","deployment_environment_name"]
  structuredMetadata:
    pod: pod  # Set structured metadata "pod" from label "pod"
  # namespaces:
  #   - meta
  #   - prod

nodeLogs:
  enabled: true
  collector: alloy-logs

alloy-operator:
  deploy: true


# applicationObservability:
#   # -- Enable receiving Application Observability.
#   # @section -- Features - Application Observability
#   enabled: false

# autoInstrumentation:
#   # -- Enable automatic instrumentation for applications.
#   # @section -- Features - Auto-Instrumentation
#   enabled: false

# annotationAutodiscovery:
#   # -- Enable gathering metrics from Kubernetes Pods and Services discovered by special annotations.
#   # @section -- Features - Annotation Autodiscovery
#   enabled: false

# prometheusOperatorObjects:
#   # -- Enable gathering metrics from Prometheus Operator Objects.
#   # @section -- Features - Prometheus Operator Objects
#   enabled: false

# profiling:
#   # -- Enable gathering profiles from applications.
#   # @section -- Features - Profiling
#   enabled: false


destinations:
  - name: grafana-mimir
    type: prometheus
    url: http://grafana-mimir-nginx.infrastructure.svc:80/api/v1/push
    # auth:
    #   type: basic
    #   username: "admin"
    #   password: "admin123"
  # - name: localPrometheus
  #   type: prometheus
  #   url: http://grafana-mimir-nginx.infrastructure.svc:80/api/v1/push
    
  - name: grafana-loki
    type: loki
    url: http://grafana-loki-gateway.infrastructure.svc/loki/api/v1/push
    # url: http://mimir-nginx.mimir-test.svc:80/loki/api/v1/push
    # auth:
    #   type: basic
    #   username: "my-username"
    #   password: "my-password"
    #   tenantIdFrom: env("LOKI_TENANT_ID")

# Inject arbitrary k8s resources alongside the chart’s own manifests
extraObjects:
  # 1) Grant pods/log permissions
  - apiVersion: rbac.authorization.k8s.io/v1
    kind: ClusterRole
    metadata:
      name: alloy-logs-podreader
    rules:
      - apiGroups: [""]
        resources:
          - pods
          - pods/log
          - namespaces
        verbs:
          - get
          - list
          - watch

  # 2) Bind it to the alloy-logs ServiceAccount
  - apiVersion: rbac.authorization.k8s.io/v1
    kind: ClusterRoleBinding
    metadata:
      name: alloy-logs-podreader-binding
    subjects:
      - kind: ServiceAccount
        name: grafana-alloy-alloy-logs   # adjust to your release name
        namespace: monitoring             # adjust to your namespace
    roleRef:
      apiGroup: rbac.authorization.k8s.io
      kind: ClusterRole
      name: alloy-logs-podreader