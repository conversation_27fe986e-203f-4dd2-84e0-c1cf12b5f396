global:
  # inject all keys from this Secret as env vars
  extraEnvFrom:
    - secretRef:
        name: mimir-s3-credentials
  # bump this to force a pod-rolling restart when creds rotate
  podAnnotations:
    bucketSecretVersion: "0"
  
  #If you don’t need Mimi<PERSON>’s internal traces, simply tell the SDK not to configure any span exporter. Add this to your Helm values.yaml so all Mimir components get the setting:
  # extraEnv: #rollback - comment it ; TRY!
  #   - name: OTEL_TRACES_EXPORTER
  #     value: "none"    

  # these env vars will be added to every Mimir pod (distributor, ingester, querier, etc.)
  extraEnv:
    - name: OTEL_EXPORTER_OTLP_TRACES_ENDPOINT
      # point at your Alloy or Tempo collector’s HTTP OTLP receiver
      value: "http://grafana-tempo.infrastructure.svc.cluster.local:4318/v1/traces"
    - name: OTEL_EXPORTER_OTLP_INSECURE
      # Alloy/Tempo’s receiver is usually plain-HTTP in-cluster
      value: "true"


metaMonitoring:
  serviceMonitor:
    enabled: true
  grafanaAgent:
    enabled: false
    # installOperator: true
    # metrics:
    #   additionalRemoteWriteConfigs:
    #     - url: "http://grafana-mimir-nginx.infrastructure.svc:80/api/v1/push"
    # tolerations:
    #   - key: "CriticalAddonsOnly"
    #     operator: "Exists"
    #     effect: "NoSchedule"

mimir:
  structuredConfig:
    limits:
      # This configuration ensures that all metrics data older than 30 days is automatically deleted from object storage
      compactor_blocks_retention_period: 20d
      # bump the global series/User limit from 150 000 → 500 000
      max_global_series_per_user: 500000
    
    # auth: # NOT WORKING IN HELM values! https://github.com/grafana/mimir/issues/5101
    #   multitenancy_enabled: true #in order to dont get anonymous users in logs/ metrics
    #   tenant_header: "X-Scope-OrgID"  

    # If you only want to raise the limit for a single tenant (in your case anonymous), use Mimir’s runtime-config:  
    # overrides:
    #   anonymous:
    #     # raise just this tenant’s global series/User cap
    #     max_global_series_per_user: 500000      
    common:
      storage:
        backend: s3
        s3:
          endpoint: s3.amazonaws.com
          access_key_id: ${AWS_ACCESS_KEY_ID}
          secret_access_key:  ${AWS_SECRET_ACCESS_KEY}
          region: eu-central-1      
    blocks_storage:
      s3:
        bucket_name: grafana-mimir-storage-prod
    alertmanager_storage:
      s3:
        bucket_name: grafana-mimir-alert-prod
    ruler_storage:
      s3:
        bucket_name: grafana-mimir-rules-storage-prod
minio:
  enabled: false      

distributor:
  enabled: true
  replicas: 3            # number of distributor pods
  extraArgs:    
    # default was 10000 and 200000
    distributor.ingestion-rate-limit:  "150000"
    distributor.ingestion-burst-size:  "300000"

ingester:
  enabled: true
  replicas: 3
  persistentVolume:
    enabled: true
    size: 10Gi

store_gateway:
  persistentVolume:
    enabled: true
    size: 10Gi

compactor:
  persistentVolume:
    enabled: true
    size: 10Gi  

alertmanager:
  persistentVolume:
    enabled: true
    size: 10Gi


chunks-cache:
  # -- Specifies whether memcached based chunks-cache should be enabled
  enabled: true
  # -- Total number of chunks-cache replicas
  replicas: 1
  # -- Amount of memory allocated to chunks-cache for object storage (in MB).
  allocatedMemory: 8192


#segment duration
#chunks retention period
#blocks retention period

#write ahead log  duration 2h -3h