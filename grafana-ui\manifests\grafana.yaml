---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-datasources
  namespace: infrastructure
data:
  datasources.yaml: |-
    apiVersion: 1
    datasources:
      - name: Mimir
        type: prometheus
        access: server
        url: http://grafana-mimir-nginx.infrastructure.svc:80/prometheus
        isDefault: true      
      - name: Loki
        type: loki
        #access: proxy # if behind LB
        access: server
        url: http://grafana-loki-gateway.infrastructure.svc
        isDefault: false
        editable: true
      - name: Tempo
        type: tempo
        uid: tempo
        #access: proxy
        access: server
        editable: true
        url: http://grafana-tempo.infrastructure.svc.cluster.local:3200
        basicAuth: false
        jsonData:
          httpMethod: POST
          tracesToLogsV2:
            datasourceUid: Loki
            spanStartTimeShift: "-1h"
            spanEndTimeShift: "1h"
            #tags: ['job', 'instance', 'pod', 'namespace']
            #filterByTraceID: false
            #filterBySpanID: false
            #customQuery: true
            #query: 'method="$${__span.tags.method}"'
          tracesToMetrics:
            datasourceUid: Mimir
            spanStartTimeShift: '-1h'
            spanEndTimeShift: '1h'
            #tags: [{ key: 'service.name', value: 'service' }, { key: 'job' }]
            #queries:
            #  - name: 'Sample query'
            #    query: 'sum(rate(traces_spanmetrics_latency_bucket{$$__tags}[5m]))'            
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-ini
  namespace: infrastructure
data:
  grafana.ini: |
    #################################### Authentication ##############################
    [auth.azuread]
    name                           = Azure AD
    enabled                        = true
    allow_sign_up                  = true
    auto_login                     = false
    client_authentication          = client_secret_post
    client_id                      = ${GF_AUTH_AZUREAD_CLIENT_ID}
    client_secret                  = ${GF_AUTH_AZUREAD_CLIENT_SECRET}
    scopes                         = openid email profile
    auth_url                       = https://login.microsoftonline.com/<TENANT_ID>/oauth2/v2.0/authorize
    token_url                      = https://login.microsoftonline.com/<TENANT_ID>/oauth2/v2.0/token
    allowed_organizations          = <TENANT_ID>
    role_attribute_strict          = false
    allow_assign_grafana_admin     = false
    skip_org_role_sync             = false
    use_pkce                       = true
---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-pvc
  namespace: infrastructure
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: grafana
  name: grafana
  namespace: infrastructure
spec:
  strategy:
    type: Recreate # otherwise the PVC remount issue
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      securityContext:
        fsGroup: 472
        supplementalGroups:
          - 0
      containers:
        - name: grafana
          image: grafana/grafana:latest
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 3000
              name: http-grafana
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /robots.txt
              port: 3000
              scheme: HTTP
            initialDelaySeconds: 10
            periodSeconds: 30
            successThreshold: 1
            timeoutSeconds: 2
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 3000
            timeoutSeconds: 1
          resources:
            requests:
              cpu: 250m
              memory: 750Mi
            limits:
              cpu: 1
              memory: 2Gi
          env:
            - name: GF_SECURITY_ADMIN_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: grafana-ui-admin-password
                  key: password              
            # Azure AD OAuth env-vars:
            - name: GF_AUTH_AZUREAD_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: grafana-azuread-auth
                  key: client-id
            - name: GF_AUTH_AZUREAD_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: grafana-azuread-auth
                  key: client-secret
          volumeMounts:
            - mountPath: /var/lib/grafana
              name: grafana-pv
            - mountPath: /etc/grafana/provisioning/datasources
              name: grafana-datasources              
            - mountPath: /etc/grafana/grafana.ini
              name: grafana-ini
              subPath: grafana.ini
      volumes:
        - name: grafana-pv
          persistentVolumeClaim:
            claimName: grafana-pvc
        - name: grafana-datasources
          configMap:
            name: grafana-datasources            
        - name: grafana-ini
          configMap:
            name: grafana-ini
---
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: infrastructure
spec:
  ports:
    - port: 3000
      protocol: TCP
      targetPort: http-grafana
  selector:
    app: grafana
  sessionAffinity: None
  type: LoadBalancer