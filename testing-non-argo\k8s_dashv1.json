{"annotations": {"list": [{"$$hashKey": "object:247", "builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "【English version】2025.01.25 Updates，kubernetesComprehensive display of resources！Includes K8S Overall Resource Overview、Microservices Resource Details、Pod Resource Details andK8SNetwork Bandwidth，Optimization metrics。https://grafana.com/orgs/starsliao/dashboards", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3, "links": [{"icon": "bolt", "tags": [], "targetBlank": true, "title": "Update", "tooltip": "See more dashboards", "type": "link", "url": "https://grafana.com/orgs/starsliao/dashboards"}, {"$$hashKey": "object:831", "icon": "question", "tags": ["node_exporter"], "targetBlank": true, "title": "GitHub", "tooltip": "View more dashboards", "type": "link", "url": "https://github.com/starsliao"}], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 54, "panels": [], "title": "Node Resource Overview：Selected Nodes:【$Node】", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 0.8}, {"color": "red", "value": 0.9}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 1}, "id": 44, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"}) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})", "format": "time_series", "hide": false, "instant": true, "interval": "", "legendFormat": "Memory Utilization", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"}) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})", "format": "time_series", "hide": false, "instant": true, "interval": "", "legendFormat": "Memory Request Rate", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"}) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})", "format": "time_series", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "Memory Limit Rate", "refId": "B", "step": 10}], "title": "Node Memory Ratio", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 0.7}, {"color": "red", "value": 0.9}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 1}, "id": 45, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum (irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"}[2m])) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})", "format": "time_series", "hide": false, "instant": true, "interval": "", "legendFormat": "CPU Utilization Rate", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"}) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})", "format": "time_series", "instant": true, "interval": "", "legendFormat": "CPU Request Rate", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"}) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})", "format": "time_series", "instant": true, "interval": "", "legendFormat": "CPU Limit Rate", "refId": "B"}], "title": "Node CPU Ratio", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "Number of cluster nodes，Nodes POD Number of，NodesPODUpper Limit", "fieldConfig": {"defaults": {"mappings": [], "max": 100, "min": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 1000}, {"color": "red", "value": 2000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 8, "y": 1}, "id": 74, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "count(kube_node_info{origin_prometheus=~\"$origin_prometheus\"})", "instant": true, "interval": "", "legendFormat": "Number of nodes", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "count(kube_pod_info{origin_prometheus=~\"$origin_prometheus\",created_by_kind!~\"<none>|Job\",node=~\"^$Node$\"})", "hide": false, "instant": true, "interval": "", "legendFormat": "Pod Number of nodes", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"pods\", unit=\"integer\",node=~\"^$Node$\"})", "hide": false, "instant": true, "interval": "", "legendFormat": "Upper limit Pod", "refId": "C"}], "title": "Nodes with Pod", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "center", "cellOptions": {"type": "color-text"}, "filterable": false, "inspect": false}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Space"}, "properties": [{"id": "custom.width", "value": 59}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Pod"}, "properties": [{"id": "custom.width", "value": 21}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "SVC"}, "properties": [{"id": "custom.width", "value": 7}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Microservices"}, "properties": [{"id": "custom.width", "value": 4}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Configuration"}, "properties": [{"id": "custom.width", "value": 16}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Passwords"}, "properties": [{"id": "custom.width", "value": 33}]}]}, "gridPos": {"h": 8, "w": 5, "x": 11, "y": 1}, "id": 51, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": true}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Microservices"}]}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "count(kube_pod_info{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\"}) by (namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "count(kube_service_info{origin_prometheus=~\"$origin_prometheus\"}) by(namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "count by (namespace)({__name__=~\"kube_deployment_metadata_generation|kube_daemonset_metadata_generation|kube_statefulset_metadata_generation\",origin_prometheus=~\"$origin_prometheus\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "__auto", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "count(kube_configmap_info{origin_prometheus=~\"$origin_prometheus\"}) by(namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "configmap", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "count(kube_secret_info{origin_prometheus=~\"$origin_prometheus\"}) by(namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "secret", "refId": "E"}], "title": "Namespace Resource Statistics", "transformations": [{"id": "seriesToColumns", "options": {"byField": "namespace"}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true}, "includeByName": {}, "indexByName": {"Time 1": 2, "Time 2": 4, "Time 3": 6, "Value #A": 3, "Value #C": 5, "Value #D": 1, "namespace": 0}, "renameByName": {"Time 1": "", "Time 2": "", "Value #A": "Pod", "Value #B": "Configuration", "Value #C": "SVC", "Value #D": "Microservices", "Value #E": "Passwords", "namespace": "Spaces"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "series", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 30, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 4, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 1}, "id": 32, "options": {"legend": {"calcs": ["max"], "displayMode": "list", "placement": "bottom", "showLegend": true, "width": 200}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum (irate(container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m]))*8", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Receive", "metric": "network", "range": true, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum (irate(container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m]))*8", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Send", "metric": "network", "refId": "B", "step": 10}], "title": "$NameSpace：Network Overview（Associable nodes and namespaces）", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "max": 2000000000000, "min": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 100000000000}, {"color": "red", "value": 2000000000000}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 5}, "id": 71, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Total Memory", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Usage", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Requests", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Limits", "refId": "B"}], "title": "Node Memory Information", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "max": 500, "min": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 500}, {"color": "red", "value": 1000}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 5}, "id": 72, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Total Cores", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",id=\"/\",node=~\"^$Node$\"}[2m]))", "instant": true, "interval": "", "legendFormat": "Usage", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Requests", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Limit", "refId": "B"}], "title": "Node CPU Number of cores", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [], "max": 8000000000000, "min": 1, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 5000000000000}, {"color": "red", "value": 10000000000000}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Utilization"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "max"}, {"id": "min", "value": 0}, {"id": "thresholds", "value": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "orange", "value": 80}, {"color": "red", "value": 90}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total"}, "properties": [{"id": "decimals", "value": 0}]}]}, "gridPos": {"h": 4, "w": 3, "x": 8, "y": 5}, "id": 73, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "vertical", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showUnfilled": false, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "sum (container_fs_usage_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"}) / sum (container_fs_limit_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Utilization Rate", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "sum (container_fs_usage_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Usage", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "sum (container_fs_limit_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"})", "instant": true, "interval": "", "legendFormat": "Total", "refId": "B"}], "title": "Node Storage Information", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/Anomaly.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 9}, "id": 88, "maxPerRow": 2, "options": {"colorMode": "background", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "showPercentChange": false, "text": {}, "textMode": "auto", "wideLayout": true}, "pluginVersion": "10.4.1", "repeat": "origin_prometheus", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count({__name__=~\"kube_deployment_metadata_generation|kube_daemonset_metadata_generation|kube_statefulset_metadata_generation\",origin_prometheus=~\"$origin_prometheus\"})", "hide": false, "instant": true, "legendFormat": "Workload", "range": false, "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count(kube_pod_info{origin_prometheus=~\"$origin_prometheus\"})", "hide": false, "instant": true, "legendFormat": "Total Pod", "range": false, "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count by(key,origin_prometheus)(kube_node_spec_taint{origin_prometheus=~\"$origin_prometheus\",key=~\"node.kubernetes.io.*\"})", "format": "time_series", "hide": false, "instant": true, "legendFormat": "{{key}}", "range": false, "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count by(origin_prometheus)(kube_node_info{origin_prometheus=~\"$origin_prometheus\"})", "hide": false, "instant": true, "legendFormat": "Total Nodes", "range": false, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count by(origin_prometheus)(kube_node_info{origin_prometheus=~\"$origin_prometheus\"}) - count by(origin_prometheus)(kube_node_spec_taint{origin_prometheus=~\"$origin_prometheus\",key!~\"node.kubernetes.io.*\"})", "hide": false, "instant": true, "legendFormat": "Normal Node", "range": false, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count by(key,origin_prometheus)(kube_node_spec_taint{origin_prometheus=~\"$origin_prometheus\",key!~\"node.kubernetes.io.*\"})", "hide": false, "instant": true, "legendFormat": "{{key}}", "range": false, "refId": "A"}], "title": "", "transformations": [{"id": "renameByRegex", "options": {"regex": "(node.kubernetes.io/)(.*)", "renamePattern": "Abnormal:$2"}}], "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Memory"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}, {"id": "custom.lineWidth", "value": 2}]}]}, "gridPos": {"h": 5, "w": 8, "x": 0, "y": 11}, "id": 79, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})", "instant": false, "interval": "", "legendFormat": "Total Memory", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"})", "instant": false, "interval": "", "legendFormat": "Usage", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"})", "hide": true, "instant": false, "interval": "", "legendFormat": "Requests", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"})", "hide": true, "instant": false, "interval": "", "legendFormat": "Limit", "refId": "B"}], "title": "Memory Usage【$Node】", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Cores"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}, {"id": "custom.lineWidth", "value": 2}]}]}, "gridPos": {"h": 5, "w": 8, "x": 8, "y": 11}, "id": 80, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})", "instant": false, "interval": "", "legendFormat": "Total Cores", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",id=\"/\",node=~\"^$Node$\"}[2m]))", "instant": false, "interval": "", "legendFormat": "Usage", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"})", "hide": true, "instant": false, "interval": "", "legendFormat": "Requests", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"})", "hide": true, "instant": true, "interval": "", "legendFormat": "Limit", "refId": "B"}], "title": "CPU Used Cores【$Node】", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "description": "Number of Cluster Nodes，Nodes POD Number of，Nodes POD Upper Limit", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "series", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Upper Limit Pod"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}, {"id": "custom.lineWidth", "value": 2}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Number of nodes"}, "properties": [{"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.drawStyle", "value": "points"}, {"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}, {"id": "custom.pointSize", "value": 3}]}]}, "gridPos": {"h": 5, "w": 8, "x": 16, "y": 11}, "id": 81, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "count(kube_node_info{origin_prometheus=~\"$origin_prometheus\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "Number of nodes", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "count(kube_pod_info{origin_prometheus=~\"$origin_prometheus\",created_by_kind!~\"<none>|Job\",node=~\"^$Node$\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "Pod Number", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"pods\", unit=\"integer\",node=~\"^$Node$\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "Upper Limit Pod", "refId": "C"}], "title": "Pod Number and nodes【$Node】", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/Total number of cores.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "#C4162A", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 16}, "id": 75, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"}[2m]))by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})by (node)*100", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{node}}", "refId": "I"}], "title": "$Node：Nodes CPU Breakdown", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 16}, "id": 76, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"})by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})by (node)*100", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{node}}", "refId": "I"}], "title": "$Node：Node Memory Breakdown", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "series", "axisLabel": "←Inflow/Outflow→", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/Inflow.*/"}, "properties": [{"id": "custom.transform", "value": "negative-Y"}]}]}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 16}, "id": 78, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (irate(container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\"}[2m]))by (node) *8", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Inflow:{{node}}", "metric": "network", "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum (irate(container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\"}[2m]))by (node) *8", "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Outflows:{{node}}", "metric": "network", "refId": "B", "step": 10}], "title": "$Node：Node Network Overview", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Limitations"}, "properties": [{"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Usage"}, "properties": [{"id": "custom.width", "value": 71}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Limits"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Disk Usage"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "byRegexp", "options": ".*%"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "color", "value": {"mode": "continuous-GrYlRd"}}, {"id": "custom.width", "value": 85}, {"id": "unit", "value": "percentunit"}, {"id": "decimals", "value": 0}]}, {"matcher": {"id": "byRegexp", "options": "(Memory Usage|Total Memory|Memory Request|Memory Limit|Disk Usage|Disk Total)"}, "properties": [{"id": "unit", "value": "bytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Nodes"}, "properties": [{"id": "custom.width", "value": 96}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Requests"}, "properties": [{"id": "custom.width", "value": 76}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Requests"}, "properties": [{"id": "custom.width", "value": 75}]}, {"matcher": {"id": "byRegexp", "options": "(CPUTotal|MemoryTotal|DiskTotal|PodLimit)"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "blue"}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "PodCap"}, "properties": [{"id": "custom.width", "value": 66}]}, {"matcher": {"id": "byRegexp", "options": "CPU Core Usage$|Memory Usage$|Disk Usage$|Pod Number of"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "color-text"}}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*Total/"}, "properties": [{"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}, {"id": "decimals", "value": 0}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Pod Number of"}, "properties": [{"id": "custom.width", "value": 58}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPUTotal Cores"}, "properties": [{"id": "custom.width", "value": 69}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Memory"}, "properties": [{"id": "custom.width", "value": 75}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Disk"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Core Usage"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Usage%"}, "properties": [{"id": "custom.width", "value": 102}]}]}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 22}, "id": 52, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Memory Usage%"}]}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "count(kube_pod_info{origin_prometheus=~\"$origin_prometheus\",created_by_kind!~\"<none>|Job\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "pod Number", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "kube_node_status_condition{origin_prometheus=~\"$origin_prometheus\",status=\"true\",node=~\"^$Node$\"}  == 1", "format": "table", "hide": true, "instant": true, "interval": "", "legendFormat": "Status", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"}[2m])) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "I"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"} - 0", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "J"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"}) by (node) - 0", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "G"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "H"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_fs_usage_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "K"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_fs_limit_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"}) by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "L"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"})by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Memory Usage%", "refId": "M"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"})by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Memory Requests%", "refId": "N"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",node=~\"^$Node$\"})by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\", node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Memory Limit%", "refId": "O"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container!=\"\",node=~\"^$Node$\"}[2m]))by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "CPU Usage%", "refId": "P"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"})by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "CPU Requests%", "refId": "Q"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",node=~\"^$Node$\"})by (node) / sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\", node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Memory Limit%", "refId": "R"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_fs_usage_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"})by (node) / sum (container_fs_limit_bytes{origin_prometheus=~\"$origin_prometheus\",device=~\"^/dev/.*$\",id=\"/\",node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Disk Usage%", "refId": "S"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_node_status_allocatable{origin_prometheus=~\"$origin_prometheus\",resource=\"pods\", unit=\"integer\",node=~\"^$Node$\"})by (node)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "PodCaps", "refId": "T"}], "title": "$Node：Node Information Detail", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 10": true, "Time 11": true, "Time 12": true, "Time 13": true, "Time 14": true, "Time 15": true, "Time 16": true, "Time 17": true, "Time 18": true, "Time 19": true, "Time 2": true, "Time 20": true, "Time 3": true, "Time 4": true, "Time 5": true, "Time 6": true, "Time 7": true, "Time 8": true, "Time 9": true, "Value #B": true, "Value #E": false, "Value #F": false, "__name__": true, "app_kubernetes_io_name": true, "app_kubernetes_io_name 1": true, "app_kubernetes_io_name 2": true, "app_kubernetes_io_name 3": true, "app_kubernetes_io_version": true, "app_kubernetes_io_version 1": true, "app_kubernetes_io_version 2": true, "app_kubernetes_io_version 3": true, "condition": true, "instance": true, "instance 1": true, "instance 2": true, "instance 3": true, "job": true, "job 1": true, "job 2": true, "job 3": true, "k8s_namespace": true, "k8s_namespace 1": true, "k8s_namespace 2": true, "k8s_namespace 3": true, "k8s_sname": true, "k8s_sname 1": true, "k8s_sname 2": true, "k8s_sname 3": true, "origin_prometheus": true, "origin_prometheus 1": true, "origin_prometheus 2": true, "origin_prometheus 3": true, "resource": true, "status": true, "unit": true}, "includeByName": {}, "indexByName": {"Time": 22, "Value #A": 2, "Value #C": 6, "Value #D": 8, "Value #E": 16, "Value #F": 17, "Value #G": 18, "Value #H": 19, "Value #I": 7, "Value #J": 9, "Value #K": 11, "Value #L": 10, "Value #M": 4, "Value #N": 13, "Value #O": 15, "Value #P": 3, "Value #Q": 12, "Value #R": 14, "Value #S": 5, "Value #T": 1, "instance": 23, "job": 24, "node": 0, "origin_prometheus": 25, "resource": 20, "unit": 21}, "renameByName": {"Value #A": "Pod Number of", "Value #C": "CPU Total Cores", "Value #D": "Total Memory", "Value #E": "CPU Requests", "Value #F": "CPU Limit", "Value #G": "Memory Requests", "Value #H": "Memory Limit", "Value #I": "CPU Core Usage", "Value #J": "Memory Usage", "Value #K": "Disk Usage", "Value #L": "Disk Total", "Value #M": "Memory Usage%", "Value #N": "Memory Requests%", "Value #O": "Memory Limit%", "Value #P": "CPU Usage%", "Value #Q": "CPU Requests%", "Value #R": "CPU Limit%", "Value #S": "Disk Usage%", "Value #T": "Pod Limit", "condition": "Status", "node": "Node"}}}, {"id": "filterFieldsByName", "options": {}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"align": "center", "cellOptions": {"type": "color-background"}, "inspect": false}, "decimals": 0, "links": [], "mappings": [{"options": {"0": {"color": "red", "index": 0}}, "type": "value"}, {"options": {"match": "null", "result": {"color": "red", "index": 1}}, "type": "special"}], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Utilization"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "custom.width", "value": 54}, {"id": "color", "value": {"fixedColor": "purple", "mode": "fixed"}}, {"id": "decimals"}, {"id": "mappings", "value": [{"options": {"from": 75, "result": {"color": "semi-dark-red", "index": 0}, "to": 110}, "type": "range"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Mounts Pod Number of"}, "properties": [{"id": "unit", "value": "none"}, {"id": "custom.width", "value": 59}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Namespace"}, "properties": [{"id": "custom.width", "value": 58}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "PVC"}, "properties": [{"id": "custom.width", "value": 94}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Usage"}, "properties": [{"id": "custom.width", "value": 57}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total"}, "properties": [{"id": "custom.width", "value": 54}]}]}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 33}, "id": 92, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Usage Rate"}]}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace,persistentvolumeclaim) (kubelet_volume_stats_used_bytes{origin_prometheus=~\"$origin_prometheus\"})", "format": "table", "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{namespace}}:{{ persistentvolumeclaim }}", "metric": "container_memory_usage:sort_desc", "range": false, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "min by (namespace,persistentvolumeclaim) (kubelet_volume_stats_available_bytes{origin_prometheus=~\"$origin_prometheus\"}) + max by (namespace,persistentvolumeclaim) (kubelet_volume_stats_used_bytes{origin_prometheus=~\"$origin_prometheus\"})", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "__auto", "metric": "container_memory_usage:sort_desc", "range": false, "refId": "B", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max by (namespace,persistentvolumeclaim) (kubelet_volume_stats_used_bytes{origin_prometheus=~\"$origin_prometheus\"}) /(min by (namespace,persistentvolumeclaim) (kubelet_volume_stats_available_bytes{origin_prometheus=~\"$origin_prometheus\"}) + max by (namespace,persistentvolumeclaim) (kubelet_volume_stats_used_bytes{origin_prometheus=~\"$origin_prometheus\"}))*100", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{namespace}}:{{ persistentvolumeclaim }}", "metric": "container_memory_usage:sort_desc", "range": false, "refId": "C", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count by (namespace,persistentvolumeclaim)(kube_pod_spec_volumes_persistentvolumeclaims_info{origin_prometheus=~\"$origin_prometheus\"})", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 1, "legendFormat": "__auto", "metric": "container_memory_usage:sort_desc", "range": false, "refId": "D", "step": 10}], "title": "PVC Storage Usage", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true}, "includeByName": {}, "indexByName": {}, "renameByName": {"Value #A": "Usage", "Value #B": "Total", "Value #C": "Usage Rate", "Value #D": "Mount Pod Number", "namespace": "Namespaces", "persistentvolumeclaim": "PVC"}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 6, "y": 33}, "id": 86, "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container !=\"\",container!=\"POD\"}[2m])) by (namespace)>0.5", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{ namespace }}", "metric": "container_cpu", "refId": "A", "step": 10}], "title": "Namespaces CPU Usage kernel(>0.5)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 8, "w": 9, "x": 15, "y": 33}, "id": 85, "options": {"legend": {"calcs": ["last", "max"], "displayMode": "table", "placement": "right", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container !=\"\",container!=\"POD\"}) by (namespace) > 1*1024*1024*1024", "interval": "", "intervalFactor": 1, "legendFormat": "{{namespace} {{ pod }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "A", "step": 10}], "title": "Namespaces WSS Memory Usage (>1G)", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 41}, "id": 49, "panels": [], "title": "Pod Resource Overview：SelectedPod:【$Pod】", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "displayName": "", "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "percentage", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 80}, {"color": "red", "value": 90}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Namespace"}, "properties": [{"id": "custom.width", "value": 96}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Pod Name"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "custom.align", "value": "right"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Cores Used"}, "properties": [{"id": "custom.width", "value": 71}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Reboot"}, "properties": [{"id": "custom.width", "value": 38}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "#EAB839", "value": 1}, {"color": "red", "value": 3}]}}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}, {"id": "decimals"}]}, {"matcher": {"id": "byRegexp", "options": ".*%"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "color", "value": {"mode": "continuous-GrYlRd"}}, {"id": "decimals", "value": 1}, {"id": "custom.width", "value": 55}, {"id": "min", "value": 0}, {"id": "max", "value": 1}]}, {"matcher": {"id": "byRegexp", "options": ".*Restrictions"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "color-background"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Nodes"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "byRegexp", "options": "Cores in use$|WSS$|RSS$|Survival|Inflow|Outflow"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "color-text"}}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Container Name"}, "properties": [{"id": "custom.width", "value": 57}, {"id": "custom.align", "value": "left"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Survival"}, "properties": [{"id": "unit", "value": "s"}, {"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Used cores"}, "properties": [{"id": "custom.width", "value": 62}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Limits"}, "properties": [{"id": "custom.width", "value": 58}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Limit"}, "properties": [{"id": "custom.width", "value": 68}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Memory Requirements"}, "properties": [{"id": "custom.width", "value": 88}]}, {"matcher": {"id": "byRegexp", "options": "WSS$|RSS$|Memory Requirements$|Memory Limit$|Disk.*$"}, "properties": [{"id": "unit", "value": "bytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "WSS"}, "properties": [{"id": "custom.width", "value": 81}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "RSS"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "CPU Requirements"}, "properties": [{"id": "custom.width", "value": 72}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Disk Limitations"}, "properties": [{"id": "custom.width", "value": 83}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Disk Usage"}, "properties": [{"id": "custom.width", "value": 72}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 10737418240}, {"color": "red", "value": 16106127360}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "WSS%"}, "properties": [{"id": "custom.width", "value": 77}]}, {"matcher": {"id": "byRegexp", "options": "/Inflow|Streaming Out/"}, "properties": [{"id": "unit", "value": "binbps"}, {"id": "custom.width", "value": 80}]}]}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 42}, "id": 47, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "WSS%"}]}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (container, pod,node,namespace) / (sum(container_spec_cpu_quota{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}/100000) by (container, pod,node,namespace)) ", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (container, pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "CPUKernel Usage", "refId": "Q"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",pod=~\"$Pod\",container =~\"$Container\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",pod=~\"$Pod\",container =~\"$Container\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)/ sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "wss%", "refId": "I"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "wss", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)/ sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "rss%", "refId": "L"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "rss", "refId": "K"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",pod=~\"$Pod\",container =~\"$Container\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",pod=~\"$Pod\",container =~\"$Container\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(container_fs_usage_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "J"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "kube_pod_container_status_restarts_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"} * on (pod) group_left(node) kube_pod_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "H"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "time() - kube_pod_created{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\"} * on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\",container =~\"$Container\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "__auto", "refId": "R"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(container_fs_limit_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "S"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(sum(irate(container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\",container =~\"$Container\"}) by(pod) *8", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "__auto", "refId": "T"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(sum(irate(container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\",container =~\"$Container\"}) by(pod) *8", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "__auto", "refId": "U"}], "title": "$Node：Pod Resource Detail(Associable Nodes)", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 10": true, "Time 11": true, "Time 12": true, "Time 13": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true, "Time 6": true, "Time 7": true, "Time 8": true, "Time 9": true, "Value #G": false, "__name__": true, "app_kubernetes_io_name": true, "app_kubernetes_io_name 1": true, "app_kubernetes_io_name 2": true, "app_kubernetes_io_version": true, "app_kubernetes_io_version 1": true, "app_kubernetes_io_version 2": true, "container 1": true, "container 10": true, "container 11": true, "container 12": true, "container 2": true, "container 3": true, "container 4": true, "container 5": true, "container 6": true, "container 7": true, "container 8": true, "container 9": true, "created_by_kind": true, "created_by_name": true, "host_ip": true, "instance": true, "instance 1": true, "instance 2": true, "job": true, "job 1": true, "job 2": true, "k8s_namespace": true, "k8s_namespace 1": true, "k8s_namespace 2": true, "k8s_sname": true, "k8s_sname 1": true, "k8s_sname 2": true, "namespace": false, "namespace 1": true, "namespace 10": true, "namespace 11": true, "namespace 12": false, "namespace 2": true, "namespace 3": true, "namespace 4": true, "namespace 5": true, "namespace 6": true, "namespace 7": true, "namespace 8": true, "namespace 9": true, "node 1": true, "node 10": true, "node 11": false, "node 12": true, "node 2": true, "node 3": true, "node 4": true, "node 5": true, "node 6": true, "node 7": true, "node 8": true, "node 9": true, "origin_prometheus": true, "origin_prometheus 1": true, "origin_prometheus 2": true, "phase": true, "pod_ip": true, "priority_class": true, "uid": true}, "includeByName": {}, "indexByName": {"Time": 21, "Value #A": 4, "Value #B": 16, "Value #C": 7, "Value #D": 10, "Value #E": 17, "Value #F": 9, "Value #G": 23, "Value #H": 14, "Value #I": 5, "Value #J": 13, "Value #K": 11, "Value #L": 6, "Value #M": 24, "Value #N": 25, "Value #O": 26, "Value #P": 27, "Value #Q": 8, "Value #R": 15, "Value #S": 12, "container": 2, "instance": 18, "ip": 28, "job": 19, "namespace": 1, "node": 0, "origin_prometheus": 20, "pod": 3, "uid": 22}, "renameByName": {"Value #A": "CPU%", "Value #B": "CPU Requirements", "Value #C": "CPU Limitations", "Value #D": "WSS", "Value #E": "Memory Requirements", "Value #F": "Memory Limit", "Value #H": "Reboot", "Value #I": "WSS%", "Value #J": "Disk Usage", "Value #K": "RSS", "Value #L": "RSS%", "Value #M": "Heap Memory", "Value #N": "max<PERSON>eap", "Value #O": "Non-Heap", "Value #P": "max<PERSON><PERSON>-<PERSON><PERSON>", "Value #Q": "Using Cores", "Value #R": "Survival", "Value #S": "Disk Limit", "Value #T": "Inflow", "Value #U": "Streaming Out", "container": "Container Name", "instance": "", "ip": "POD IP", "namespace": "Namespace", "namespace 1": "", "namespace 12": "Namespace", "node": "Nodes", "node 1": "", "node 11": "Node", "pod": "Pod Name", "priority_class": ""}}}, {"id": "filterFieldsByName", "options": {"include": {"names": ["Node", "Namespace", "Container Name", "Pod Name", "CPU%", "WSS%", "RSS%", "CPU Restrictions", "Cores Used", "Memory Limit", "WSS", "RSS", "Disk Limit", "Disk Usage", "Reboot", "CPUDemand", "Memory Requirements", "Inflow", "Outflow", "Survival"]}}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 50}, "id": 58, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "max(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (container, pod) / (max(container_spec_cpu_quota{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}/100000) by (container, pod)) * 100", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{ pod }}", "metric": "container_cpu", "refId": "A", "step": 10}], "title": "Pod Containers CPU Utilization (Maximum100%Associable Nodes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 50}, "id": 27, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod)/ max(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod) * 100", "interval": "", "intervalFactor": 1, "legendFormat": "WSS：{{ pod }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod)/ max(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod) * 100", "interval": "", "intervalFactor": 1, "legendFormat": "RSS：{{ pod }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "B", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "(cass_jvm_heap{service=~\"$Container\"} * on (pod_ip) group_right(service) kube_pod_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\"}) / (cass_jvm_heap_max{service=~\"$Container\"} * on (pod_ip) group_right(service) kube_pod_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\"}) * 100", "hide": true, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Heap：{{ pod }}", "metric": "container_memory_usage:sort_desc", "refId": "C", "step": 10}], "title": "Pod Container Memory Usage (Associatable Nodes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 50}, "id": 77, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max(max(irate(container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\",container =~\"$Container\"}) by(pod) *8", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Inflow:{{ pod}}", "metric": "network", "range": true, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max(max(irate(container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",namespace=~\"$NameSpace\",container =~\"$Container\"}) by(pod) *8", "interval": "", "intervalFactor": 1, "legendFormat": "Outflow:{{ pod}}", "metric": "network", "range": true, "refId": "B", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "sum(sum(irate(container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",image!=\"\",name=~\"^k8s_.*\",node=~\"^$Node$\",namespace=~\"$NameSpace\",pod=~\".*$Container.*\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info) by(pod) *8", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "__auto", "metric": "network", "range": true, "refId": "C", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "sum(sum(irate(container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",image!=\"\",name=~\"^k8s_.*\",node=~\"^$Node$\",namespace=~\"$NameSpace\",pod=~\".*$Container.*\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info) by(pod) *8", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "__auto", "metric": "network", "range": true, "refId": "D", "step": 10}], "title": "Pod Network bandwidth per second (Associable Nodes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/Limit.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 59}, "id": 82, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "max(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (container, pod,node,namespace)", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU Usage：{{ pod }}", "metric": "container_cpu", "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "max(max(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",pod=~\"$Pod\",container =~\"$Container\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)) by(container)", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "Pod CPU Limitations：{{ container}}", "metric": "container_cpu", "refId": "B", "step": 10}], "title": "Pod Containers CPU Core Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/.*Limitations/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 59}, "id": 84, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)", "interval": "", "intervalFactor": 1, "legendFormat": "WSS：{{ pod }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "max(max(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",pod=~\"$Pod\",container =~\"$Container\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container,pod,node,namespace)) by(container)", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "PodMemory Limits：{{ container}}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "B", "step": 10}], "title": "Pod Containers WSS Memory Usage (Associable Nodes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 59}, "id": 83, "options": {"legend": {"calcs": ["lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "12.0.2", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "max (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",container =~\"$Container\",container !=\"\",container!=\"POD\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}) by (container, pod,node,namespace)", "interval": "", "intervalFactor": 1, "legendFormat": "RSS：{{ pod }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "A", "step": 10}], "title": "Pod Containers RSS Memory Usage (Associable Nodes)", "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 68}, "id": 61, "panels": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "cellOptions": {"type": "auto"}, "filterable": false, "inspect": false}, "displayName": "", "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": ".*%"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.cellOptions", "value": {"mode": "gradient", "type": "gauge", "valueDisplayMode": "color"}}, {"id": "max", "value": 1}, {"id": "min", "value": 0}, {"id": "color", "value": {"mode": "continuous-GrYlRd"}}, {"id": "decimals", "value": 0}]}, {"matcher": {"id": "byRegexp", "options": ".*Memory Usage$|.*Memory Limits$|.*Memory Requirements$|.*Disk Usage$|.*Disk Limits$"}, "properties": [{"id": "unit", "value": "bytes"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Namespace"}, "properties": [{"id": "custom.width", "value": 92}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Container Name"}, "properties": [{"id": "custom.width", "value": 187}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total CPU Core Usage"}, "properties": [{"id": "custom.width", "value": 100}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Pod"}, "properties": [{"id": "custom.width", "value": 44}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Average CPU Usage%"}, "properties": [{"id": "custom.width", "value": 116}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Average RSS Memory Usage%"}, "properties": [{"id": "custom.width", "value": 141}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Average WSS Memory Usage%"}, "properties": [{"id": "custom.width", "value": 165}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total CPU Limit"}, "properties": [{"id": "custom.width", "value": 86}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Memory Limit"}, "properties": [{"id": "custom.width", "value": 86}]}, {"matcher": {"id": "byRegexp", "options": "/.*Limit$/"}, "properties": [{"id": "color", "value": {"fixedColor": "blue", "mode": "fixed"}}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}]}, {"matcher": {"id": "byRegexp", "options": "/.*Memory Usage$|.*Core Usage$/"}, "properties": [{"id": "custom.cellOptions", "value": {"type": "color-text"}}, {"id": "color", "value": {"fixedColor": "orange", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total RSS Memory Usage"}, "properties": [{"id": "custom.width", "value": 107}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total WSS Memory Usage"}, "properties": [{"id": "custom.width", "value": 113}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Average Disk Usage"}, "properties": [{"id": "custom.width", "value": 96}, {"id": "custom.cellOptions", "value": {"type": "color-background"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "orange", "value": 10737418240}, {"color": "red", "value": 16106127360}]}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Average Disk Limit"}, "properties": [{"id": "custom.width", "value": 96}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total CPU Demand"}, "properties": [{"id": "custom.width", "value": 80}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total Memory Demand"}, "properties": [{"id": "custom.width", "value": 80}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 3}, "id": 87, "options": {"cellHeight": "sm", "footer": {"countRows": false, "enablePagination": true, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Average WSS Memory Usage%"}]}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}[2m])) by (container) / (sum(container_spec_cpu_quota{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}/100000) by (container))", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}[2m])) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Total Cores Used", "refId": "L"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)/ sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "I"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "D"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)/ sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Average Memory%(RSS)", "refId": "H"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "Total Memory Usage (RSS) ", "refId": "K"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "sum by(container) (kube_pod_container_resource_requests{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"})", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "E"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": false, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"memory\", unit=\"byte\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "F"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "avg(container_fs_usage_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "J"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "count(kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by(container,namespace)", "format": "table", "instant": true, "interval": "", "legendFormat": "", "refId": "G"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": false, "expr": "avg(container_fs_limit_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "M"}], "title": "Microservices (Container Name) Resource Statistics", "transformations": [{"id": "merge", "options": {}}, {"id": "organize", "options": {"excludeByName": {"Time": true, "Time 1": true, "Time 10": true, "Time 11": true, "Time 12": true, "Time 2": true, "Time 3": true, "Time 4": true, "Time 5": true, "Time 6": true, "Time 7": true, "Time 8": true, "Time 9": true}, "includeByName": {}, "indexByName": {"Time": 15, "Value #A": 3, "Value #B": 13, "Value #C": 6, "Value #D": 9, "Value #E": 14, "Value #F": 8, "Value #G": 2, "Value #H": 5, "Value #I": 4, "Value #J": 12, "Value #K": 10, "Value #L": 7, "Value #M": 11, "container": 1, "namespace": 0}, "renameByName": {"Time 1": "", "Value #A": "Average CPU Usage%", "Value #B": "Total CPU Demand", "Value #C": "Total CPU Restrictions", "Value #D": "Total WSS Memory Usage", "Value #E": "Total Memory Requirements", "Value #F": "Total Memory Limit", "Value #G": "Pod", "Value #H": "Average RSS Memory Usage%", "Value #I": "Average WSS Memory Usage%", "Value #J": "Average Disk Usage", "Value #K": "Total RSS Memory Use", "Value #L": "Total CPU Core Usage", "Value #M": "Average Disk Limit", "container": "Container Name", "namespace": "Namespace"}}}, {"id": "filterFieldsByName", "options": {}}], "type": "table"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 12}, "id": 24, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "exemplar": true, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}[2m])) by (container) / (sum(container_spec_cpu_quota{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}/100000) by (container)) * 100", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{ container}}", "metric": "container_cpu", "refId": "A", "step": 10}], "title": "Microservices (Container Name) Average CPU Usage (Maximum100%)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 12}, "id": 89, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)/ sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container) * 100", "interval": "", "intervalFactor": 1, "legendFormat": "WSS：{{ container }}", "metric": "container_memory_usage:sort_desc", "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "sum (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)/ sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container) * 100", "interval": "", "intervalFactor": 1, "legendFormat": "RSS：{{ container }}", "metric": "container_memory_usage:sort_desc", "refId": "B", "step": 10}], "title": "Microservice (Container Name) Average Memory Utilization", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "binbps"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 12}, "id": 16, "options": {"legend": {"calcs": ["mean", "last", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(sum(irate(container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",namespace=~\"$NameSpace\",container =~\"$Container\"}) by(container) *8", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Inflow:{{ container }}", "metric": "network", "range": true, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(sum(irate(container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",node=~\"^$Node$\",namespace=~\"$NameSpace\"}[2m])) by (pod)* on(pod) group_right kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",namespace=~\"$NameSpace\",container =~\"$Container\"}) by(container) *8", "interval": "", "intervalFactor": 1, "legendFormat": "Outflow:{{ container }}", "metric": "network", "range": true, "refId": "B", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "sum (rate (container_network_receive_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",image!=\"\",name=~\"^k8s_.*\",node=~\"^$Node$\",namespace=~\"$NameSpace\",pod=~\".*$Container.*\"}[2m])) by (pod)", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "-> {{ pod }}", "metric": "network", "refId": "C", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "expr": "- sum (rate (container_network_transmit_bytes_total{origin_prometheus=~\"$origin_prometheus\",pod=~\"$Pod\",image!=\"\",name=~\"^k8s_.*\",node=~\"^$Node$\",namespace=~\"$NameSpace\",pod=~\".*$Container.*\"}[2m])) by (pod)", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "<- {{ pod }}", "metric": "network", "refId": "D", "step": 10}], "title": "Microservice (Container Name) Network bandwidth per second (Associable Nodes)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/CPU Limit.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 19}, "id": 91, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(kube_pod_container_resource_limits{origin_prometheus=~\"$origin_prometheus\",resource=\"cpu\", unit=\"core\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU Limitations：{{ container}}", "metric": "container_cpu", "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(container_cpu_usage_seconds_total{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}[2m])) by (container)", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "CPU Core Usage：{{ container}}", "metric": "container_cpu", "refId": "B", "step": 10}], "title": "Microservices (Container Name) Overall CPU Cores Used", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "byRegexp", "options": "/Memory Limit.*/"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}]}, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 19}, "id": 90, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true, "sortBy": "Max", "sortDesc": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "sum (container_memory_working_set_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "interval": "", "intervalFactor": 1, "legendFormat": "WSS：{{ container }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "A", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "sum(container_spec_memory_limit_bytes{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "interval": "", "intervalFactor": 1, "legendFormat": "Memory Limit：{{ container }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "B", "step": 10}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "sum (container_memory_rss{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by (container)", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "RSS：{{ container }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "C", "step": 10}], "title": "Microservices (Container Name) Overall Memory Usage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 19}, "id": 59, "options": {"legend": {"calcs": ["max", "last", "mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "10.4.1", "targets": [{"datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "editorMode": "code", "expr": "count(kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",container =~\"$Container\",container !=\"\",container!=\"POD\",namespace=~\"$NameSpace\"}) by(container,namespace)", "interval": "", "intervalFactor": 1, "legendFormat": "{{namespace}}：{{ container }}", "metric": "container_memory_usage:sort_desc", "range": true, "refId": "A", "step": 10}], "title": "Microservice (Container Name) Pod Number", "type": "timeseries"}], "title": "Microservices (Container Name) Resource Overview：Selected Microservices:【$Container】", "type": "row"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": ["Prometheus", "Kubernetes"], "templating": {"list": [{"current": {"text": "", "value": ""}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(kube_node_info,origin_prometheus)", "includeAll": false, "label": "K8S", "name": "origin_prometheus", "options": [], "query": {"query": "label_values(kube_node_info,origin_prometheus)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(kube_node_info{origin_prometheus=~\"$origin_prometheus\"},node)", "includeAll": true, "label": "Nodes", "name": "Node", "options": [], "query": {"query": "label_values(kube_node_info{origin_prometheus=~\"$origin_prometheus\"},node)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(kube_namespace_created{origin_prometheus=~\"$origin_prometheus\"},namespace)", "includeAll": true, "label": "Namespace", "name": "NameSpace", "options": [], "query": {"query": "label_values(kube_namespace_created{origin_prometheus=~\"$origin_prometheus\"},namespace)", "refId": "PrometheusVariableQueryEditor-VariableQuery"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",namespace=~\"$NameSpace\"},container)", "includeAll": true, "label": "Microservice (Container Name)", "name": "Container", "options": [], "query": {"query": "label_values(kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",namespace=~\"$NameSpace\"},container)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}, {"allValue": ".*", "current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "PAE45454D0EDB9216"}, "definition": "label_values(kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",namespace=~\"$NameSpace\",container=~\"$Container\"},pod)", "includeAll": true, "label": "Pod", "name": "Pod", "options": [], "query": {"query": "label_values(kube_pod_container_info{origin_prometheus=~\"$origin_prometheus\",namespace=~\"$NameSpace\",container=~\"$Container\"},pod)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 5, "type": "query"}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["30s", "1m", "5m", "15m", "30m", "1h"]}, "timezone": "browser", "title": "K8S Dashboard ndi v1", "version": 1}