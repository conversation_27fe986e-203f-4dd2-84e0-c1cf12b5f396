#helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
#helm repo update
#helm install --namespace monitoring kube-state-metrics prometheus-community/kube-state-metrics -f kube-state-metrics-values.yaml --create-namespace

prometheusScrape: true
replicas: 1
service:
  port: 8080
  # Default to clusterIP for backward compatibility
  type: ClusterIP

prometheus:
  monitor:
    enabled: false

customResourceState:
  # Whether to enable support for CustomResourceStateMetrics.
  enabled: false


# Enable self metrics configuration for service and Service Monitor
# Default values for telemetry configuration can be overridden
# If you set telemetryNodePort, you must also set service.type to NodePort
selfMonitor:
  enabled: false
  # telemetryHost: 0.0.0.0
  # telemetryPort: 8081
  # telemetryNodePort: 0