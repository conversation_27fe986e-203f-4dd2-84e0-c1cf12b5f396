---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
  creationTimestamp: null
  name: podmonitors.monitoring.coreos.com
spec:
  group: monitoring.coreos.com
  names:
    categories:
    - prometheus-operator
    kind: PodMonitor
    listKind: PodMonitorList
    plural: podmonitors
    shortNames:
    - pmon
    singular: podmonitor
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              attachMetadata:
                properties:
                  node:
                    type: boolean
                type: object
              jobLabel:
                type: string
              labelLimit:
                format: int64
                type: integer
              labelNameLengthLimit:
                format: int64
                type: integer
              labelValueLengthLimit:
                format: int64
                type: integer
              namespaceSelector:
                properties:
                  any:
                    type: boolean
                  matchNames:
                    items:
                      type: string
                    type: array
                type: object
              podMetricsEndpoints:
                items:
                  properties:
                    authorization:
                      properties:
                        credentials:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        type:
                          type: string
                      type: object
                    basicAuth:
                      properties:
                        password:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        username:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    bearerTokenSecret:
                      properties:
                        key:
                          type: string
                        name:
                          type: string
                        optional:
                          type: boolean
                      required:
                      - key
                      type: object
                      x-kubernetes-map-type: atomic
                    enableHttp2:
                      type: boolean
                    filterRunning:
                      type: boolean
                    followRedirects:
                      type: boolean
                    honorLabels:
                      type: boolean
                    honorTimestamps:
                      type: boolean
                    interval:
                      pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                      type: string
                    metricRelabelings:
                      items:
                        properties:
                          action:
                            default: replace
                            enum:
                            - replace
                            - Replace
                            - keep
                            - Keep
                            - drop
                            - Drop
                            - hashmod
                            - HashMod
                            - labelmap
                            - LabelMap
                            - labeldrop
                            - LabelDrop
                            - labelkeep
                            - LabelKeep
                            - lowercase
                            - Lowercase
                            - uppercase
                            - Uppercase
                            - keepequal
                            - KeepEqual
                            - dropequal
                            - DropEqual
                            type: string
                          modulus:
                            format: int64
                            type: integer
                          regex:
                            type: string
                          replacement:
                            type: string
                          separator:
                            type: string
                          sourceLabels:
                            items:
                              pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                              type: string
                            type: array
                          targetLabel:
                            type: string
                        type: object
                      type: array
                    oauth2:
                      properties:
                        clientId:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        clientSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        endpointParams:
                          additionalProperties:
                            type: string
                          type: object
                        scopes:
                          items:
                            type: string
                          type: array
                        tokenUrl:
                          minLength: 1
                          type: string
                      required:
                      - clientId
                      - clientSecret
                      - tokenUrl
                      type: object
                    params:
                      additionalProperties:
                        items:
                          type: string
                        type: array
                      type: object
                    path:
                      type: string
                    port:
                      type: string
                    proxyUrl:
                      type: string
                    relabelings:
                      items:
                        properties:
                          action:
                            default: replace
                            enum:
                            - replace
                            - Replace
                            - keep
                            - Keep
                            - drop
                            - Drop
                            - hashmod
                            - HashMod
                            - labelmap
                            - LabelMap
                            - labeldrop
                            - LabelDrop
                            - labelkeep
                            - LabelKeep
                            - lowercase
                            - Lowercase
                            - uppercase
                            - Uppercase
                            - keepequal
                            - KeepEqual
                            - dropequal
                            - DropEqual
                            type: string
                          modulus:
                            format: int64
                            type: integer
                          regex:
                            type: string
                          replacement:
                            type: string
                          separator:
                            type: string
                          sourceLabels:
                            items:
                              pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                              type: string
                            type: array
                          targetLabel:
                            type: string
                        type: object
                      type: array
                    scheme:
                      enum:
                      - http
                      - https
                      type: string
                    scrapeTimeout:
                      pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                      type: string
                    targetPort:
                      anyOf:
                      - type: integer
                      - type: string
                      x-kubernetes-int-or-string: true
                    tlsConfig:
                      properties:
                        ca:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        cert:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        insecureSkipVerify:
                          type: boolean
                        keySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        serverName:
                          type: string
                      type: object
                  type: object
                type: array
              podTargetLabels:
                items:
                  type: string
                type: array
              sampleLimit:
                format: int64
                type: integer
              selector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              targetLimit:
                format: int64
                type: integer
            required:
            - podMetricsEndpoints
            - selector
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
