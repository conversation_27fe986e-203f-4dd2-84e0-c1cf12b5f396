loki:
  schemaConfig:
    configs:
      - from: "2024-04-01"
        store: tsdb
        object_store: s3
        schema: v13
        index:
          prefix: loki_index_
          period: 24h
  ingester:
    chunk_encoding: snappy
  querier:
    # Default is 4, if you have enough memory and CPU you can increase, reduce if OOMing
    max_concurrent: 4
  pattern_ingester:
    enabled: true
  limits_config:
    allow_structured_metadata: true
    volume_enabled: true
  auth_enabled: false  
deploymentMode: SimpleScalable
backend:
  replicas: 2
read:
  replicas: 2
write:
  replicas: 3 # To ensure data durability with replication
# Enable minio for storage
minio:
  enabled: true
gateway:
  service:
    #type: LoadBalancer
    type: NodePort
chunksCache:
 allocatedMemory: 8192 
  # resources:
  #   requests:
  #     cpu: 100m
  #     memory: 128Mi
  #   limits:
  #     cpu: 200m
  #     memory: 256Mi
limits_config:
  # global or per-tenant ingestion rate (MB/s)
  ingestion_rate_mb: 3
  # allowed burst size (MB) per distributor
  ingestion_burst_size_mb: 6


lokiCanary:
  enabled: true
  # Drop *all* default tolerations (including the CriticalAddonsOnly one)
  tolerations: []

  # If you want to explicitly bind to worker nodes only, clear any
  # default nodeSelector, then either use nodeSelector or affinity:
  nodeSelector: {}

  # Exclude any node labeled karpenter.sh/controller=true
  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: karpenter.sh/controller
                #operator: NotIn      # or “DoesNotExist” if you prefer
                operator: DoesNotExist
                # values:
                #   - "true"

