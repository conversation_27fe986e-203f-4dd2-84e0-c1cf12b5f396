    // Feature: Cluster Events
    declare "cluster_events" {
        argument "logs_destinations" {
          comment = "Must be a list of log destinations where collected logs should be forwarded to"
        }
  
        loki.source.kubernetes_events "cluster_events" {
          job_name   = "integrations/kubernetes/eventhandler"
          log_format = "logfmt"
          forward_to = [loki.process.cluster_events.receiver]
        }
  
        loki.process "cluster_events" {
  
          // add a static source label to the logs so they can be differentiated / restricted if necessary
          stage.static_labels {
            values = {
              "source" = "kubernetes-events",
            }
          }
  
          // extract some of the fields from the log line, these could be used as labels, structured metadata, etc.
          stage.logfmt {
            mapping = {
              "component" = "sourcecomponent", // map the sourcecomponent field to component
              "kind" = "",
              "level" = "type", // most events don't have a level but they do have a "type" i.e. Normal, Warning, Error, etc.
              "name" = "",
              "node" = "sourcehost", // map the sourcehost field to node
              "reason" = "",
            }
          }
          // set these values as labels, they may or may not be used as index labels in Loki as they can be dropped
          // prior to being written to <PERSON>, but this makes them available
          stage.labels {
            values = {
              "component" = "",
              "kind" = "",
              "level" = "",
              "name" = "",
              "node" = "",
              "reason" = "",
            }
          }
  
          // if kind=Node, set the node label by copying the name field
          stage.match {
            selector = "{kind=\"Node\"}"
  
            stage.labels {
              values = {
                "node" = "name",
              }
            }
          }
  
          // set the level extracted key value as a normalized log level
          stage.match {
            selector = "{level=\"Normal\"}"
  
            stage.static_labels {
              values = {
                level = "Info",
              }
            }
          }
          // set the structured metadata values
          stage.structured_metadata {
            values = {
              "name" = "name",
            }
          }
  
          // Only keep the labels that are defined in the `keepLabels` list.
          stage.label_keep {
            values = ["job","level","namespace","node","source","reason"]
          }
          stage.labels {
            values = {
              "service_name" = "job",
            }
          }
          forward_to = argument.logs_destinations.value
        }
      }
      cluster_events "feature" {
        logs_destinations = [
          loki.write.grafana_loki.receiver,
        ]
      }
      // Feature: Node Logs
      declare "node_logs" {
        argument "logs_destinations" {
          comment = "Must be a list of log destinations where collected logs should be forwarded to"
        }
  
        loki.relabel "journal" {
  
          // copy all journal labels and make the available to the pipeline stages as labels, there is a label
          // keep defined to filter out unwanted labels, these pipeline labels can be set as structured metadata
          // as well, the following labels are available:
          // - boot_id
          // - cap_effective
          // - cmdline
          // - comm
          // - exe
          // - gid
          // - hostname
          // - machine_id
          // - pid
          // - stream_id
          // - systemd_cgroup
          // - systemd_invocation_id
          // - systemd_slice
          // - systemd_unit
          // - transport
          // - uid
          //
          // More Info: https://www.freedesktop.org/software/systemd/man/systemd.journal-fields.html
          rule {
            action = "labelmap"
            regex = "__journal__(.+)"
          }
  
          rule {
            action = "replace"
            source_labels = ["__journal__systemd_unit"]
            replacement = "$1"
            target_label = "unit"
          }
  
          // the service_name label will be set automatically in loki if not set, and the unit label
          // will not allow service_name to be set automatically.
          rule {
            action = "replace"
            source_labels = ["__journal__systemd_unit"]
            replacement = "$1"
            target_label = "service_name"
          }
  
          forward_to = [] // No forward_to is used in this component, the defined rules are used in the loki.source.journal component
        }
  
        loki.source.journal "worker" {
          path = "/var/log/journal"
          format_as_json = false
          max_age = "8h"
          relabel_rules = loki.relabel.journal.rules
          labels = {
            job = "integrations/kubernetes/journal",
            instance = sys.env("HOSTNAME"),
          }
          forward_to = [loki.process.journal_logs.receiver]
        }
  
        loki.process "journal_logs" {
          stage.static_labels {
            values = {
              // add a static source label to the logs so they can be differentiated / restricted if necessary
              "source" = "journal",
              // default level to unknown
              level = "unknown",
            }
          }
  
          // Attempt to determine the log level, most k8s workers are either in logfmt or klog formats
          // check to see if the log line matches the klog format (https://github.com/kubernetes/klog)
          stage.match {
            // unescaped regex: ([IWED][0-9]{4}\s+[0-9]{2}:[0-9]{2}:[0-9]{2}\.[0-9]+)
            selector = "{level=\"unknown\"} |~ \"([IWED][0-9]{4}\\\\s+[0-9]{2}:[0-9]{2}:[0-9]{2}\\\\.[0-9]+)\""
  
            // extract log level, klog uses a single letter code for the level followed by the month and day i.e. I0119
            stage.regex {
              expression = "((?P<level>[A-Z])[0-9])"
            }
  
            // if the extracted level is I set INFO
            stage.replace {
              source = "level"
              expression = "(I)"
              replace = "INFO"
            }
  
            // if the extracted level is W set WARN
            stage.replace {
              source = "level"
              expression = "(W)"
              replace = "WARN"
            }
  
            // if the extracted level is E set ERROR
            stage.replace {
              source = "level"
              expression = "(E)"
              replace = "ERROR"
            }
  
            // if the extracted level is I set INFO
            stage.replace {
              source = "level"
              expression = "(D)"
              replace = "DEBUG"
            }
  
            // set the extracted level to be a label
            stage.labels {
              values = {
                level = "",
              }
            }
          }
  
          // if the level is still unknown, do one last attempt at detecting it based on common levels
          stage.match {
            selector = "{level=\"unknown\"}"
  
            // unescaped regex: (?i)(?:"(?:level|loglevel|levelname|lvl|levelText|SeverityText)":\s*"|\s*(?:level|loglevel|levelText|lvl)="?|\s+\[?)(?P<level>(DEBUG?|DBG|INFO?(RMATION)?|WA?RN(ING)?|ERR(OR)?|CRI?T(ICAL)?|FATAL|FTL|NOTICE|TRACE|TRC|PANIC|PNC|ALERT|EMERGENCY))("|\s+|-|\s*\])
            stage.regex {
              expression = "(?i)(?:\"(?:level|loglevel|levelname|lvl|levelText|SeverityText)\":\\s*\"|\\s*(?:level|loglevel|levelText|lvl)=\"?|\\s+\\[?)(?P<level>(DEBUG?|DBG|INFO?(RMATION)?|WA?RN(ING)?|ERR(OR)?|CRI?T(ICAL)?|FATAL|FTL|NOTICE|TRACE|TRC|PANIC|PNC|ALERT|EMERGENCY))(\"|\\s+|-|\\s*\\])"
            }
  
            // set the extracted level to be a label
            stage.labels {
              values = {
                level = "",
              }
            }
          }
  
          // Only keep the labels that are defined in the `keepLabels` list.
          stage.label_keep {
            values = ["instance","job","level","name","unit","service_name","source"]
          }
  
          forward_to = argument.logs_destinations.value
        }
      }
      node_logs "feature" {
        logs_destinations = [
          loki.write.grafana_loki.receiver,
        ]
      }
      // Feature: Pod Logs
      declare "pod_logs" {
        argument "logs_destinations" {
          comment = "Must be a list of log destinations where collected logs should be forwarded to"
        }
  
        discovery.relabel "filtered_pods" {
          targets = discovery.kubernetes.pods.targets
          rule {
            source_labels = ["__meta_kubernetes_namespace"]
            action = "replace"
            target_label = "namespace"
          }
          rule {
            source_labels = ["__meta_kubernetes_pod_name"]
            action = "replace"
            target_label = "pod"
          }
          rule {
            source_labels = ["__meta_kubernetes_pod_container_name"]
            action = "replace"
            target_label = "container"
          }
          rule {
            source_labels = ["__meta_kubernetes_namespace", "__meta_kubernetes_pod_container_name"]
            separator = "/"
            action = "replace"
            replacement = "$1"
            target_label = "job"
          }
  
          // set the container runtime as a label
          rule {
            action = "replace"
            source_labels = ["__meta_kubernetes_pod_container_id"]
            regex = "^(\\S+):\\/\\/.+$"
            replacement = "$1"
            target_label = "tmp_container_runtime"
          }
  
          // make all labels on the pod available to the pipeline as labels,
          // they are omitted before write to loki via stage.label_keep unless explicitly set
          rule {
            action = "labelmap"
            regex = "__meta_kubernetes_pod_label_(.+)"
          }
  
          // make all annotations on the pod available to the pipeline as labels,
          // they are omitted before write to loki via stage.label_keep unless explicitly set
          rule {
            action = "labelmap"
            regex = "__meta_kubernetes_pod_annotation_(.+)"
          }
  
          // explicitly set service_name. if not set, loki will automatically try to populate a default.
          // see https://grafana.com/docs/loki/latest/get-started/labels/#default-labels-for-all-users
          //
          // choose the first value found from the following ordered list:
          // - pod.annotation[resource.opentelemetry.io/service.name]
          // - pod.label[app.kubernetes.io/name]
          // - k8s.pod.name
          // - k8s.container.name
          rule {
            action = "replace"
            source_labels = [
              "__meta_kubernetes_pod_annotation_resource_opentelemetry_io_service_name",
              "__meta_kubernetes_pod_label_app_kubernetes_io_name",
              "__meta_kubernetes_pod_container_name",
            ]
            separator = ";"
            regex = "^(?:;*)?([^;]+).*$"
            replacement = "$1"
            target_label = "service_name"
          }
  
          // set resource attributes
          rule {
            action = "labelmap"
            regex = "__meta_kubernetes_pod_annotation_resource_opentelemetry_io_(.+)"
          }
          rule {
            source_labels = ["__meta_kubernetes_pod_annotation_k8s_grafana_com_logs_job"]
            regex = "(.+)"
            target_label = "job"
          }
          rule {
            source_labels = ["__meta_kubernetes_pod_label_app_kubernetes_io_name"]
            regex = "(.+)"
            target_label = "app_kubernetes_io_name"
          }
        }
  
        discovery.kubernetes "pods" {
          role = "pod"
          selectors {
            role = "pod"
            field = "spec.nodeName=" + sys.env("HOSTNAME")
          }
        }
  
        discovery.relabel "filtered_pods_with_paths" {
          targets = discovery.relabel.filtered_pods.output
  
          rule {
            source_labels = ["__meta_kubernetes_pod_uid", "__meta_kubernetes_pod_container_name"]
            separator = "/"
            action = "replace"
            replacement = "/var/log/pods/*$1/*.log"
            target_label = "__path__"
          }
        }
  
        local.file_match "pod_logs" {
          path_targets = discovery.relabel.filtered_pods_with_paths.output
        }
  
        loki.source.file "pod_logs" {
          targets    = local.file_match.pod_logs.targets
          forward_to = [loki.process.pod_logs.receiver]
        }
  
        loki.process "pod_logs" {
          stage.match {
            selector = "{tmp_container_runtime=~\"containerd|cri-o\"}"
            // the cri processing stage extracts the following k/v pairs: log, stream, time, flags
            stage.cri {}
  
            // Set the extract flags and stream values as labels
            stage.labels {
              values = {
                flags  = "",
                stream  = "",
              }
            }
          }
  
          stage.match {
            selector = "{tmp_container_runtime=\"docker\"}"
            // the docker processing stage extracts the following k/v pairs: log, stream, time
            stage.docker {}
  
            // Set the extract stream value as a label
            stage.labels {
              values = {
                stream  = "",
              }
            }
          }
  
          // Drop the filename label, since it's not really useful in the context of Kubernetes, where we already have cluster,
          // namespace, pod, and container labels. Drop any structured metadata. Also drop the temporary
          // container runtime label as it is no longer needed.
          stage.label_drop {
            values = [
              "filename",
              "tmp_container_runtime",
            ]
          }
          stage.structured_metadata {
            values = {
              "k8s_pod_name" = "k8s_pod_name",
              "pod" = "pod",
            }
          }
  
          // Only keep the labels that are defined in the `keepLabels` list.
          stage.label_keep {
            values = ["app_kubernetes_io_name","container","instance","job","level","namespace","service_name","service_namespace","deployment_environment","deployment_environment_name"]
          }
  
          forward_to = argument.logs_destinations.value
        }
      }
      pod_logs "feature" {
        logs_destinations = [
          loki.write.grafana_loki.receiver,
        ]
      }
  
  
  
  
      // Destination: grafana-loki (loki)
      otelcol.exporter.loki "grafana_loki" {
        forward_to = [loki.write.grafana_loki.receiver]
      }
  
      loki.write "grafana_loki" {
        endpoint {
          url = "http://grafana-loki-gateway.infrastructure.svc/loki/api/v1/push"
          tls_config {
            insecure_skip_verify = false
          }
          min_backoff_period = "500ms"
          max_backoff_period = "5m"
          max_backoff_retries = "10"
        }
        external_labels = {
          "cluster" = "eks-observability-prod",
          "k8s_cluster_name" = "eks-observability-prod",
        }
      }