#mimir-nginx.mimir-test.svc:80

apiVersion: v1
kind: Service
metadata:
  name: grafana-mimir-nginx-nodeport
  namespace: infrastructure
spec:
  selector:
    # These selectors must match the labels of pods targeted by the original mimir-nginx service
    #app: mimir-nginx  # This is an assumption - adjust to match your actual pod labels
    #service: mimir-nginx  # This is an assumption - adjust to match your actual pod labels
    app.kubernetes.io/component: nginx    
    app.kubernetes.io/instance: grafana-mimir
    app.kubernetes.io/name: mimir
  ports:
    - port: 80         # The port exposed inside the cluster
      targetPort: 8080   # The port on the pod
      nodePort: 30080  # Port exposed on each node (must be in range 30000-32767)
      protocol: TCP
  type: NodePort