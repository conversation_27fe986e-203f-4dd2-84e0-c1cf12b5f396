    // Feature: Cluster Metrics
    declare "cluster_metrics" {
      argument "metrics_destinations" {
        comment = "Must be a list of metric destinations where collected metrics should be forwarded to"
      }
      discovery.kubernetes "nodes" {
        role = "node"
      }

      discovery.relabel "nodes" {
        targets = discovery.kubernetes.nodes.targets
        rule {
          source_labels = ["__meta_kubernetes_node_name"]
          target_label  = "node"
        }

        rule {
          replacement = "kubernetes"
          target_label = "source"
        }

      }

      // Kubelet
      discovery.relabel "kubelet" {
        targets = discovery.relabel.nodes.output
      }

      prometheus.scrape "kubelet" {
        targets  = discovery.relabel.kubelet.output
        job_name = "integrations/kubernetes/kubeletprobes"
        scheme   = "https"
        scrape_interval = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"

        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = true
          server_name = "kubernetes"
        }

        clustering {
          enabled = true
        }

        forward_to = [prometheus.relabel.kubelet.receiver]
      }

      prometheus.relabel "kubelet" {
        max_cache_size = 100000

        forward_to = argument.metrics_destinations.value
      }

      // Kubelet Resources
      discovery.relabel "kubelet_resources" {
        targets = discovery.relabel.nodes.output
        rule {
          replacement   = "/metrics/resource"
          target_label  = "__metrics_path__"
        }
      }

      prometheus.scrape "kubelet_resources" {
        targets = discovery.relabel.kubelet_resources.output
        job_name = "integrations/kubernetes/resources"
        scheme   = "https"
        scrape_interval = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"

        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = true
          server_name = "kubernetes"
        }

        clustering {
          enabled = true
        }

        forward_to = [prometheus.relabel.kubelet_resources.receiver]
      }

      prometheus.relabel "kubelet_resources" {
        max_cache_size = 100000

        forward_to = argument.metrics_destinations.value
      }

      // Kubelet Probes
      discovery.relabel "kubelet_probes" {
        targets = discovery.relabel.nodes.output
        rule {
          replacement   = "/metrics/probes"
          target_label  = "__metrics_path__"
        }
      }

      prometheus.scrape "kubelet_probes" {
        targets  = discovery.relabel.kubelet_probes.output
        job_name = "integrations/kubernetes/probes"
        scheme   = "https"
        scrape_interval = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"

        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = true
          server_name = "kubernetes"
        }

        clustering {
          enabled = true
        }

        forward_to = [prometheus.relabel.kubelet_probes.receiver]
      }

      prometheus.relabel "kubelet_probes" {
        max_cache_size = 100000

        forward_to = argument.metrics_destinations.value
      }

      // cAdvisor
      discovery.relabel "cadvisor" {
        targets = discovery.relabel.nodes.output
        rule {
          replacement   = "/metrics/cadvisor"
          target_label  = "__metrics_path__"
        }
      }

      prometheus.scrape "cadvisor" {
        targets = discovery.relabel.cadvisor.output
        job_name = "integrations/kubernetes/cadvisor"
        scheme = "https"
        scrape_interval = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"

        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = true
          server_name = "kubernetes"
        }

        clustering {
          enabled = true
        }

        forward_to = [prometheus.relabel.cadvisor.receiver]
      }

      prometheus.relabel "cadvisor" {
        max_cache_size = 100000
        // Drop empty container labels, addressing https://github.com/google/cadvisor/issues/2688
        rule {
          source_labels = ["__name__","container"]
          separator = "@"
          regex = "(container_cpu_.*|container_fs_.*|container_memory_.*)@"
          action = "drop"
        }
        // Drop empty image labels, addressing https://github.com/google/cadvisor/issues/2688
        rule {
          source_labels = ["__name__","image"]
          separator = "@"
          regex = "(container_cpu_.*|container_fs_.*|container_memory_.*|container_network_.*)@"
          action = "drop"
        }
        // Normalizing unimportant labels (not deleting to continue satisfying <label>!="" checks)
        rule {
          source_labels = ["__name__", "boot_id"]
          separator = "@"
          regex = "machine_memory_bytes@.*"
          target_label = "boot_id"
          replacement = "NA"
        }
        rule {
          source_labels = ["__name__", "system_uuid"]
          separator = "@"
          regex = "machine_memory_bytes@.*"
          target_label = "system_uuid"
          replacement = "NA"
        }
        // Filter out non-physical devices/interfaces
        rule {
          source_labels = ["__name__", "device"]
          separator = "@"
          regex = "container_fs_.*@(/dev/)?(mmcblk.p.+|nvme.+|rbd.+|sd.+|vd.+|xvd.+|dasd.+)"
          target_label = "__keepme"
          replacement = "1"
        }
        rule {
          source_labels = ["__name__", "__keepme"]
          separator = "@"
          regex = "container_fs_.*@"
          action = "drop"
        }
        rule {
          source_labels = ["__name__"]
          regex = "container_fs_.*"
          target_label = "__keepme"
          replacement = ""
        }
        rule {
          source_labels = ["__name__", "interface"]
          separator = "@"
          regex = "container_network_.*@(en[iospx][0-9].*|eth[0-9].*)"
          target_label = "__keepme"
          replacement = "1"
        }
        rule {
          source_labels = ["__name__", "__keepme"]
          separator = "@"
          regex = "container_network_.*@"
          action = "drop"
        }
        rule {
          source_labels = ["__name__"]
          regex = "container_network_.*"
          target_label = "__keepme"
          replacement = ""
        }
        forward_to = argument.metrics_destinations.value
      }

      discovery.kubernetes "apiserver" {
        role = "endpoints"

        selectors {
          role = "endpoints"
          field = "metadata.name=kubernetes"
        }

        namespaces {
          names = ["default"]
        }
      }

      discovery.relabel "apiserver" {
        targets = discovery.kubernetes.apiserver.targets

        rule {
          source_labels = ["__meta_kubernetes_endpoint_port_name"]
          regex = "https"
          action = "keep"
        }

        rule {
          source_labels = ["__meta_kubernetes_namespace"]
          target_label = "namespace"
        }

        rule {
          source_labels = ["__meta_kubernetes_service_name"]
          target_label = "service"
        }

        rule {
          replacement = "kubernetes"
          target_label = "source"
        }
      }

      prometheus.scrape "apiserver" {
        targets = discovery.relabel.apiserver.output
        job_name = "integrations/kubernetes/kube-apiserver"
        scheme = "https"
        scrape_interval = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"

        tls_config {
          ca_file = "/var/run/secrets/kubernetes.io/serviceaccount/ca.crt"
          insecure_skip_verify = false
          server_name = "kubernetes"
        }

        clustering {
          enabled = true
        }

        forward_to = [prometheus.relabel.apiserver.receiver]
      }

      prometheus.relabel "apiserver" {
        max_cache_size = 100000
        rule {
          source_labels = ["__name__"]
          regex = "up|scrape_samples_scraped|.*"
          action = "keep"
        }
        forward_to = argument.metrics_destinations.value
      }

      discovery.kubernetes "kube_controller_manager" {
        role = "pod"
        namespaces {
          names = ["kube-system"]
        }
        selectors {
          role = "pod"
          label = "component=kube-controller-manager"
        }
      }

      discovery.relabel "kube_controller_manager" {
        targets = discovery.kubernetes.kube_controller_manager.targets
        rule {
          source_labels = ["__address__"]
          replacement = "$1:10257"
          target_label = "__address__"
        }
      }

      prometheus.scrape "kube_controller_manager" {
        targets           = discovery.relabel.kube_controller_manager.output
        job_name          = "kube-controller-manager"
        scheme            = "https"
        scrape_interval   = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        tls_config {
          insecure_skip_verify = true
        }
        clustering {
          enabled = true
        }
        forward_to = argument.metrics_destinations.value
      }

      // KubeDNS
      discovery.kubernetes "kube_dns" {
        role = "endpoints"
        namespaces {
          names = ["kube-system"]
        }
        selectors {
          role = "endpoints"
          label = "k8s-app=kube-dns"
        }
      }

      discovery.relabel "kube_dns" {
        targets = discovery.kubernetes.kube_dns.targets

        // keep only the specified metrics port name, and pods that are Running and ready
        rule {
          source_labels = [
            "__meta_kubernetes_pod_container_port_name",
            "__meta_kubernetes_pod_phase",
            "__meta_kubernetes_pod_ready",
          ]
          separator = "@"
          regex = "metrics@Running@true"
          action = "keep"
        }

        // drop any init containers
        rule {
          source_labels = ["__meta_kubernetes_pod_container_init"]
          regex = "true"
          action = "drop"
        }

        // set the namespace label
        rule {
          source_labels = ["__meta_kubernetes_namespace"]
          target_label  = "namespace"
        }

        // set the pod label
        rule {
          source_labels = ["__meta_kubernetes_pod_name"]
          target_label  = "pod"
        }

        // set the container label
        rule {
          source_labels = ["__meta_kubernetes_pod_container_name"]
          target_label  = "container"
        }

        // set a workload label
        rule {
          source_labels = [
            "__meta_kubernetes_pod_controller_kind",
            "__meta_kubernetes_pod_controller_name",
          ]
          separator = "/"
          target_label  = "workload"
        }
        // remove the hash from the ReplicaSet
        rule {
          source_labels = ["workload"]
          regex = "(ReplicaSet/.+)-.+"
          target_label  = "workload"
        }

        // set the app name if specified as metadata labels "app:" or "app.kubernetes.io/name:" or "k8s-app:"
        rule {
          action = "replace"
          source_labels = [
            "__meta_kubernetes_pod_label_app_kubernetes_io_name",
            "__meta_kubernetes_pod_label_k8s_app",
            "__meta_kubernetes_pod_label_app",
          ]
          separator = ";"
          regex = "^(?:;*)?([^;]+).*$"
          replacement = "$1"
          target_label = "app"
        }

        // set the service label
        rule {
          source_labels = ["__meta_kubernetes_service_name"]
          target_label  = "service"
        }

        // set a source label
        rule {
          action = "replace"
          replacement = "kubernetes"
          target_label = "source"
        }
      }

      prometheus.scrape "kube_dns" {
        targets = discovery.relabel.kube_dns.output
        job_name = "integrations/kubernetes/kube-dns"
        scheme = "http"
        scrape_interval = "60s"
        clustering {
          enabled = true
        }
        forward_to = argument.metrics_destinations.value
      }

      discovery.kubernetes "kube_proxy" {
        role = "pod"
        namespaces {
          names = ["kube-system"]
        }
        selectors {
          role = "pod"
          label = "k8s-app=kube-proxy"
        }
      }

      discovery.relabel "kube_proxy" {
        targets = discovery.kubernetes.kube_proxy.targets
        rule {
          source_labels = ["__address__"]
          replacement = "$1:10249"
          target_label = "__address__"
        }
      }

      prometheus.scrape "kube_proxy" {
        targets           = discovery.relabel.kube_proxy.output
        job_name          = "integrations/kubernetes/kube-proxy"
        scheme            = "http"
        scrape_interval   = "60s"
        clustering {
          enabled = true
        }
        forward_to = argument.metrics_destinations.value
      }

      discovery.kubernetes "kube_scheduler" {
        role = "pod"
        namespaces {
          names = ["kube-system"]
        }
        selectors {
          role = "pod"
          label = "component=kube-scheduler"
        }
      }

      discovery.relabel "kube_scheduler" {
        targets = discovery.kubernetes.kube_scheduler.targets
        rule {
          source_labels = ["__address__"]
          replacement = "$1:10259"
          target_label = "__address__"
        }
      }

      prometheus.scrape "kube_scheduler" {
        targets           = discovery.relabel.kube_scheduler.output
        job_name          = "kube-scheduler"
        scheme            = "https"
        scrape_interval   = "60s"
        bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        tls_config {
          insecure_skip_verify = true
        }
        clustering {
          enabled = true
        }
        forward_to = argument.metrics_destinations.value
      }
      discovery.kubernetes "kube_state_metrics" {
        role = "endpoints"

        selectors {
          role = "endpoints"
          label = "app.kubernetes.io/name=kube-state-metrics,release=grafana-k8s-monitoring"
        }
        namespaces {
          names = ["monitoring"]
        }
      }

      discovery.relabel "kube_state_metrics" {
        targets = discovery.kubernetes.kube_state_metrics.targets

        // only keep targets with a matching port name
        rule {
          source_labels = ["__meta_kubernetes_pod_container_port_name"]
          regex = "http"
          action = "keep"
        }

        rule {
          action = "replace"
          replacement = "kubernetes"
          target_label = "source"
        }

      }

      prometheus.scrape "kube_state_metrics" {
        targets = discovery.relabel.kube_state_metrics.output
        job_name = "integrations/kubernetes/kube-state-metrics"
        scrape_interval = "60s"
        scheme = "http"
        bearer_token_file = ""
        tls_config {
          insecure_skip_verify = true
        }

        clustering {
          enabled = true
        }
        forward_to = argument.metrics_destinations.value
      }

      // Node Exporter
      discovery.kubernetes "node_exporter" {
        role = "pod"

        selectors {
          role = "pod"
          label = "app.kubernetes.io/name=node-exporter,release=grafana-k8s-monitoring"
        }
        namespaces {
          names = ["monitoring"]
        }
      }

      discovery.relabel "node_exporter" {
        targets = discovery.kubernetes.node_exporter.targets

        // keep only the specified metrics port name, and pods that are Running and ready
        rule {
          source_labels = [
            "__meta_kubernetes_pod_container_port_name",
            "__meta_kubernetes_pod_container_init",
            "__meta_kubernetes_pod_phase",
            "__meta_kubernetes_pod_ready",
          ]
          separator = "@"
          regex = "metrics@false@Running@true"
          action = "keep"
        }

        // Set the instance label to the node name
        rule {
          source_labels = ["__meta_kubernetes_pod_node_name"]
          action = "replace"
          target_label = "instance"
        }

        // set the namespace label
        rule {
          source_labels = ["__meta_kubernetes_namespace"]
          target_label  = "namespace"
        }

        // set the pod label
        rule {
          source_labels = ["__meta_kubernetes_pod_name"]
          target_label  = "pod"
        }

        // set the container label
        rule {
          source_labels = ["__meta_kubernetes_pod_container_name"]
          target_label  = "container"
        }

        // set a workload label
        rule {
          source_labels = [
            "__meta_kubernetes_pod_controller_kind",
            "__meta_kubernetes_pod_controller_name",
          ]
          separator = "/"
          target_label  = "workload"
        }
        // remove the hash from the ReplicaSet
        rule {
          source_labels = ["workload"]
          regex = "(ReplicaSet/.+)-.+"
          target_label  = "workload"
        }

        // set the app name if specified as metadata labels "app:" or "app.kubernetes.io/name:" or "k8s-app:"
        rule {
          action = "replace"
          source_labels = [
            "__meta_kubernetes_pod_label_app_kubernetes_io_name",
            "__meta_kubernetes_pod_label_k8s_app",
            "__meta_kubernetes_pod_label_app",
          ]
          separator = ";"
          regex = "^(?:;*)?([^;]+).*$"
          replacement = "$1"
          target_label = "app"
        }

        // set the component if specified as metadata labels "component:" or "app.kubernetes.io/component:" or "k8s-component:"
        rule {
          action = "replace"
          source_labels = [
            "__meta_kubernetes_pod_label_app_kubernetes_io_component",
            "__meta_kubernetes_pod_label_k8s_component",
            "__meta_kubernetes_pod_label_component",
          ]
          regex = "^(?:;*)?([^;]+).*$"
          replacement = "$1"
          target_label = "component"
        }

        // set a source label
        rule {
          action = "replace"
          replacement = "kubernetes"
          target_label = "source"
        }
      }

      prometheus.scrape "node_exporter" {
        targets = discovery.relabel.node_exporter.output
        job_name = "integrations/kubernetes/node_exporter"
        scrape_interval = "60s"
        scheme = "http"
        bearer_token_file = ""
        tls_config {
          insecure_skip_verify = true
        }

        clustering {
          enabled = true
        }
        forward_to = [prometheus.relabel.node_exporter.receiver]
      }

      prometheus.relabel "node_exporter" {
        max_cache_size = 100000
        // Drop metrics for certain file systems
        rule {
          source_labels = ["__name__", "fstype"]
          separator = "@"
          regex = "node_filesystem.*@(ramfs|tmpfs)"
          action = "drop"
        }
        forward_to = argument.metrics_destinations.value
      }
    }
    cluster_metrics "feature" {
      metrics_destinations = [
        prometheus.remote_write.grafana_mimir.receiver,
      ]
    }



    // Scrape Karpenter metrics every 30s and forward into your Mimir remote-write
    prometheus.scrape "karpenter_metrics" {
      job_name = "integrations/karpenter"
      targets = [
        { __address__ = "karpenter.karpenter.svc.cluster.local:8080" },
      ]
      scheme           = "http"
      scrape_interval = "30s"
      metrics_path    = "/metrics"
      //bearer_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
      tls_config {
        insecure_skip_verify = true
      }
      forward_to = [ prometheus.remote_write.grafana_mimir.receiver ]
      //forward_to = argument.metrics_destinations.value
    }
    // Destination: grafana-mimir (prometheus)
    otelcol.exporter.prometheus "grafana_mimir" {
      add_metric_suffixes = true
      resource_to_telemetry_conversion = false
      forward_to = [prometheus.remote_write.grafana_mimir.receiver]
    }

    prometheus.remote_write "grafana_mimir" {
      endpoint {
        url = "http://grafana-mimir-nginx.infrastructure.svc:80/api/v1/push"
        headers = {
        }
        tls_config {
          insecure_skip_verify = false
        }
        send_native_histograms = false

        queue_config {
          capacity = 10000
          min_shards = 1
          max_shards = 50
          max_samples_per_send = 2000
          batch_send_deadline = "5s"
          min_backoff = "30ms"
          max_backoff = "5s"
          retry_on_http_429 = true
          sample_age_limit = "0s"
        }

        write_relabel_config {
          source_labels = ["cluster"]
          regex = ""
          replacement = "eks-observability-prod"
          target_label = "cluster"
        }
        write_relabel_config {
          source_labels = ["k8s_cluster_name"]
          regex = ""
          replacement = "eks-observability-prod"
          target_label = "k8s_cluster_name"
        }
      }

      wal {
        truncate_frequency = "2h"
        min_keepalive_time = "5m"
        max_keepalive_time = "8h"
      }
    }