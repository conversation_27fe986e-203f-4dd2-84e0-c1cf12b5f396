    // Self Reporting
    prometheus.exporter.unix "kubernetes_monitoring_telemetry" {
        set_collectors = ["textfile"]
        textfile {
          directory = "/etc/alloy"
        }
      }
  
      discovery.relabel "kubernetes_monitoring_telemetry" {
        targets = prometheus.exporter.unix.kubernetes_monitoring_telemetry.targets
        rule {
          target_label = "instance"
          action = "replace"
          replacement = "grafana-k8s-monitoring"
        }
        rule {
          target_label = "job"
          action = "replace"
          replacement = "integrations/kubernetes/kubernetes_monitoring_telemetry"
        }
      }
  
      prometheus.scrape "kubernetes_monitoring_telemetry" {
        job_name   = "integrations/kubernetes/kubernetes_monitoring_telemetry"
        targets    = discovery.relabel.kubernetes_monitoring_telemetry.output
        scrape_interval = "60s"
        clustering {
          enabled = true
        }
        forward_to = [prometheus.relabel.kubernetes_monitoring_telemetry.receiver]
      }
  
      prometheus.relabel "kubernetes_monitoring_telemetry" {
        rule {
          source_labels = ["__name__"]
          regex = "grafana_kubernetes_monitoring_.*"
          action = "keep"
        }
        forward_to = [
          prometheus.remote_write.grafana_mimir.receiver,
        ]
      }
  
  
  
  
      // Destination: grafana-mimir (prometheus)
      otelcol.exporter.prometheus "grafana_mimir" {
        add_metric_suffixes = true
        resource_to_telemetry_conversion = false
        forward_to = [prometheus.remote_write.grafana_mimir.receiver]
      }
  
      prometheus.remote_write "grafana_mimir" {
        endpoint {
          url = "http://grafana-mimir-nginx.infrastructure.svc:80/api/v1/push"
          headers = {
          }
          tls_config {
            insecure_skip_verify = false
          }
          send_native_histograms = false
  
          queue_config {
            capacity = 10000
            min_shards = 1
            max_shards = 50
            max_samples_per_send = 2000
            batch_send_deadline = "5s"
            min_backoff = "30ms"
            max_backoff = "5s"
            retry_on_http_429 = true
            sample_age_limit = "0s"
          }
  
          write_relabel_config {
            source_labels = ["cluster"]
            regex = ""
            replacement = "eks-observability-prod"
            target_label = "cluster"
          }
          write_relabel_config {
            source_labels = ["k8s_cluster_name"]
            regex = ""
            replacement = "eks-observability-prod"
            target_label = "k8s_cluster_name"
          }
        }
  
        wal {
          truncate_frequency = "2h"
          min_keepalive_time = "5m"
          max_keepalive_time = "8h"
        }
      }