---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
  creationTimestamp: null
  name: probes.monitoring.coreos.com
spec:
  group: monitoring.coreos.com
  names:
    categories:
    - prometheus-operator
    kind: Probe
    listKind: ProbeList
    plural: probes
    shortNames:
    - prb
    singular: probe
  scope: Namespaced
  versions:
  - name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              authorization:
                properties:
                  credentials:
                    properties:
                      key:
                        type: string
                      name:
                        type: string
                      optional:
                        type: boolean
                    required:
                    - key
                    type: object
                    x-kubernetes-map-type: atomic
                  type:
                    type: string
                type: object
              basicAuth:
                properties:
                  password:
                    properties:
                      key:
                        type: string
                      name:
                        type: string
                      optional:
                        type: boolean
                    required:
                    - key
                    type: object
                    x-kubernetes-map-type: atomic
                  username:
                    properties:
                      key:
                        type: string
                      name:
                        type: string
                      optional:
                        type: boolean
                    required:
                    - key
                    type: object
                    x-kubernetes-map-type: atomic
                type: object
              bearerTokenSecret:
                properties:
                  key:
                    type: string
                  name:
                    type: string
                  optional:
                    type: boolean
                required:
                - key
                type: object
                x-kubernetes-map-type: atomic
              interval:
                pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                type: string
              jobName:
                type: string
              labelLimit:
                format: int64
                type: integer
              labelNameLengthLimit:
                format: int64
                type: integer
              labelValueLengthLimit:
                format: int64
                type: integer
              metricRelabelings:
                items:
                  properties:
                    action:
                      default: replace
                      enum:
                      - replace
                      - Replace
                      - keep
                      - Keep
                      - drop
                      - Drop
                      - hashmod
                      - HashMod
                      - labelmap
                      - LabelMap
                      - labeldrop
                      - LabelDrop
                      - labelkeep
                      - LabelKeep
                      - lowercase
                      - Lowercase
                      - uppercase
                      - Uppercase
                      - keepequal
                      - KeepEqual
                      - dropequal
                      - DropEqual
                      type: string
                    modulus:
                      format: int64
                      type: integer
                    regex:
                      type: string
                    replacement:
                      type: string
                    separator:
                      type: string
                    sourceLabels:
                      items:
                        pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                        type: string
                      type: array
                    targetLabel:
                      type: string
                  type: object
                type: array
              module:
                type: string
              oauth2:
                properties:
                  clientId:
                    properties:
                      configMap:
                        properties:
                          key:
                            type: string
                          name:
                            type: string
                          optional:
                            type: boolean
                        required:
                        - key
                        type: object
                        x-kubernetes-map-type: atomic
                      secret:
                        properties:
                          key:
                            type: string
                          name:
                            type: string
                          optional:
                            type: boolean
                        required:
                        - key
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  clientSecret:
                    properties:
                      key:
                        type: string
                      name:
                        type: string
                      optional:
                        type: boolean
                    required:
                    - key
                    type: object
                    x-kubernetes-map-type: atomic
                  endpointParams:
                    additionalProperties:
                      type: string
                    type: object
                  scopes:
                    items:
                      type: string
                    type: array
                  tokenUrl:
                    minLength: 1
                    type: string
                required:
                - clientId
                - clientSecret
                - tokenUrl
                type: object
              prober:
                properties:
                  path:
                    default: /probe
                    type: string
                  proxyUrl:
                    type: string
                  scheme:
                    enum:
                    - http
                    - https
                    type: string
                  url:
                    type: string
                required:
                - url
                type: object
              sampleLimit:
                format: int64
                type: integer
              scrapeTimeout:
                pattern: ^(0|(([0-9]+)y)?(([0-9]+)w)?(([0-9]+)d)?(([0-9]+)h)?(([0-9]+)m)?(([0-9]+)s)?(([0-9]+)ms)?)$
                type: string
              targetLimit:
                format: int64
                type: integer
              targets:
                properties:
                  ingress:
                    properties:
                      namespaceSelector:
                        properties:
                          any:
                            type: boolean
                          matchNames:
                            items:
                              type: string
                            type: array
                        type: object
                      relabelingConfigs:
                        items:
                          properties:
                            action:
                              default: replace
                              enum:
                              - replace
                              - Replace
                              - keep
                              - Keep
                              - drop
                              - Drop
                              - hashmod
                              - HashMod
                              - labelmap
                              - LabelMap
                              - labeldrop
                              - LabelDrop
                              - labelkeep
                              - LabelKeep
                              - lowercase
                              - Lowercase
                              - uppercase
                              - Uppercase
                              - keepequal
                              - KeepEqual
                              - dropequal
                              - DropEqual
                              type: string
                            modulus:
                              format: int64
                              type: integer
                            regex:
                              type: string
                            replacement:
                              type: string
                            separator:
                              type: string
                            sourceLabels:
                              items:
                                pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                                type: string
                              type: array
                            targetLabel:
                              type: string
                          type: object
                        type: array
                      selector:
                        properties:
                          matchExpressions:
                            items:
                              properties:
                                key:
                                  type: string
                                operator:
                                  type: string
                                values:
                                  items:
                                    type: string
                                  type: array
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                          matchLabels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  staticConfig:
                    properties:
                      labels:
                        additionalProperties:
                          type: string
                        type: object
                      relabelingConfigs:
                        items:
                          properties:
                            action:
                              default: replace
                              enum:
                              - replace
                              - Replace
                              - keep
                              - Keep
                              - drop
                              - Drop
                              - hashmod
                              - HashMod
                              - labelmap
                              - LabelMap
                              - labeldrop
                              - LabelDrop
                              - labelkeep
                              - LabelKeep
                              - lowercase
                              - Lowercase
                              - uppercase
                              - Uppercase
                              - keepequal
                              - KeepEqual
                              - dropequal
                              - DropEqual
                              type: string
                            modulus:
                              format: int64
                              type: integer
                            regex:
                              type: string
                            replacement:
                              type: string
                            separator:
                              type: string
                            sourceLabels:
                              items:
                                pattern: ^[a-zA-Z_][a-zA-Z0-9_]*$
                                type: string
                              type: array
                            targetLabel:
                              type: string
                          type: object
                        type: array
                      static:
                        items:
                          type: string
                        type: array
                    type: object
                type: object
              tlsConfig:
                properties:
                  ca:
                    properties:
                      configMap:
                        properties:
                          key:
                            type: string
                          name:
                            type: string
                          optional:
                            type: boolean
                        required:
                        - key
                        type: object
                        x-kubernetes-map-type: atomic
                      secret:
                        properties:
                          key:
                            type: string
                          name:
                            type: string
                          optional:
                            type: boolean
                        required:
                        - key
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  cert:
                    properties:
                      configMap:
                        properties:
                          key:
                            type: string
                          name:
                            type: string
                          optional:
                            type: boolean
                        required:
                        - key
                        type: object
                        x-kubernetes-map-type: atomic
                      secret:
                        properties:
                          key:
                            type: string
                          name:
                            type: string
                          optional:
                            type: boolean
                        required:
                        - key
                        type: object
                        x-kubernetes-map-type: atomic
                    type: object
                  insecureSkipVerify:
                    type: boolean
                  keySecret:
                    properties:
                      key:
                        type: string
                      name:
                        type: string
                      optional:
                        type: boolean
                    required:
                    - key
                    type: object
                    x-kubernetes-map-type: atomic
                  serverName:
                    type: string
                type: object
            type: object
        required:
        - spec
        type: object
    served: true
    storage: true
