#helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
#helm repo update
#helm install --namespace monitoring prometheus-node-exporter prometheus-community/prometheus-node-exporter -f prometheus-exporter-values.yaml --create-namespace

service:
  ## Creating a service is enabled by default
  enabled: true

  ## Service type
  type: ClusterIP
prometheus:
  monitor:
    enabled: true
    additionalLabels: {}
    namespace: ""
    jobLabel: ""
    # List of pod labels to add to node exporter metrics
    # https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md#servicemonitor
    podTargetLabels: []
    # List of target labels to add to node exporter metrics
    # https://github.com/prometheus-operator/prometheus-operator/blob/main/Documentation/api-reference/api.md#servicemonitor
    targetLabels: []  
  podMonitor:
    enabled: true    
    
tolerations:
  - effect: NoSchedule
    operator: Exists    