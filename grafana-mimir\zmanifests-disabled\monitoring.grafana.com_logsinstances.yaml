---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.9.2
  creationTimestamp: null
  name: logsinstances.monitoring.grafana.com
spec:
  group: monitoring.grafana.com
  names:
    categories:
    - agent-operator
    kind: LogsInstance
    listKind: LogsInstanceList
    plural: logsinstances
    singular: logsinstance
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            type: string
          kind:
            type: string
          metadata:
            type: object
          spec:
            properties:
              additionalScrapeConfigs:
                properties:
                  key:
                    type: string
                  name:
                    type: string
                  optional:
                    type: boolean
                required:
                - key
                type: object
                x-kubernetes-map-type: atomic
              clients:
                items:
                  properties:
                    backoffConfig:
                      properties:
                        maxPeriod:
                          type: string
                        maxRetries:
                          type: integer
                        minPeriod:
                          type: string
                      type: object
                    basicAuth:
                      properties:
                        password:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        username:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                      type: object
                    batchSize:
                      type: integer
                    batchWait:
                      type: string
                    bearerToken:
                      type: string
                    bearerTokenFile:
                      type: string
                    externalLabels:
                      additionalProperties:
                        type: string
                      type: object
                    oauth2:
                      properties:
                        clientId:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        clientSecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        endpointParams:
                          additionalProperties:
                            type: string
                          type: object
                        scopes:
                          items:
                            type: string
                          type: array
                        tokenUrl:
                          minLength: 1
                          type: string
                      required:
                      - clientId
                      - clientSecret
                      - tokenUrl
                      type: object
                    proxyUrl:
                      type: string
                    tenantId:
                      type: string
                    timeout:
                      type: string
                    tlsConfig:
                      properties:
                        ca:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        caFile:
                          type: string
                        cert:
                          properties:
                            configMap:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                            secret:
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              required:
                              - key
                              type: object
                              x-kubernetes-map-type: atomic
                          type: object
                        certFile:
                          type: string
                        insecureSkipVerify:
                          type: boolean
                        keyFile:
                          type: string
                        keySecret:
                          properties:
                            key:
                              type: string
                            name:
                              type: string
                            optional:
                              type: boolean
                          required:
                          - key
                          type: object
                          x-kubernetes-map-type: atomic
                        serverName:
                          type: string
                      type: object
                    url:
                      type: string
                  required:
                  - url
                  type: object
                type: array
              podLogsNamespaceSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              podLogsSelector:
                properties:
                  matchExpressions:
                    items:
                      properties:
                        key:
                          type: string
                        operator:
                          type: string
                        values:
                          items:
                            type: string
                          type: array
                      required:
                      - key
                      - operator
                      type: object
                    type: array
                  matchLabels:
                    additionalProperties:
                      type: string
                    type: object
                type: object
                x-kubernetes-map-type: atomic
              targetConfig:
                properties:
                  syncPeriod:
                    type: string
                type: object
            type: object
        type: object
    served: true
    storage: true
