two application sets pointed on this repo 
one for helm , second for raw yamls.

see example below of the repo structure :

aws-observability-services/
├─ grafana-mimir/                                        
│  ├─ application.yaml        # Helm app definition            ← picked up by ApplicationSet #1
│  ├─ values.yaml             # Helm values
│  └─ manifests/              #extra manifests ; plain yaml    ← picked up by ApplicationSet #2
│      ├─ crds1.yaml
│      ├─ crds2.yaml
│      └─ params.yaml      # namespace, name definition
|
├─ grafana-ui/
│  └─ manifests/              # plain YAML only       ← picked up by ApplicationSet #2
│      ├─ grafana.yaml
│      └─ params.yaml
|
├─ some-app/
│  ├─ application.yaml        # Helm app definition      ← picked up by ApplicationSet #1
│  └─ values.yaml             # Helm values
│ 


grfana tempo 

